<?php
/**
 * Keyword Research class for AI Blog Post Generator
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

class AIBPG_Keyword_Research {
    /**
     * Constructor
     */
    public function __construct() {
        // Nothing to do here for now
    }

    /**
     * Research keywords based on niche
     *
     * @param string $niche The niche to research keywords for
     * @return array Array of keywords with data
     */
    public function research_keywords($niche) {
        // Get website authority
        $website_authority = $this->get_website_authority();
        
        // Get sitemap data
        $sitemap_data = $this->analyze_sitemap();
        
        // Get existing content topics
        $existing_topics = $this->get_existing_topics();
        
        // Use AI to identify keywords
        $keywords = $this->identify_keywords_with_ai($niche, $website_authority, $sitemap_data, $existing_topics);
        
        return $keywords;
    }

    /**
     * Get website authority
     *
     * @return int Website authority score (0-100)
     */
    private function get_website_authority() {
        // In a real implementation, this would connect to an SEO API like Moz, Ahrefs, or SEMrush
        // For now, we'll use a placeholder value
        $domain = parse_url(site_url(), PHP_URL_HOST);
        
        // Check if we have cached authority
        $authority = get_transient('aibpg_website_authority');
        
        if (false === $authority) {
            // Simulate API call
            // In a real implementation, this would be an actual API call
            $authority = $this->simulate_authority_check($domain);
            
            // Cache for 7 days
            set_transient('aibpg_website_authority', $authority, 7 * DAY_IN_SECONDS);
        }
        
        return $authority;
    }

    /**
     * Simulate authority check (placeholder for actual API call)
     *
     * @param string $domain The domain to check
     * @return int Authority score
     */
    private function simulate_authority_check($domain) {
        // This is a placeholder. In a real implementation, this would call an SEO API
        // For demonstration purposes, we'll generate a random score between 10 and 50
        $authority = rand(10, 50);
        
        return $authority;
    }

    /**
     * Analyze sitemap
     *
     * @return array Sitemap analysis data
     */
    private function analyze_sitemap() {
        $sitemap_url = site_url('/sitemap.xml');
        $sitemap_data = array(
            'total_urls' => 0,
            'content_categories' => array(),
            'content_types' => array(),
            'update_frequency' => 0
        );
        
        // Check if we have cached sitemap data
        $cached_data = get_transient('aibpg_sitemap_data');
        
        if (false !== $cached_data) {
            return $cached_data;
        }
        
        // Get sitemap content
        $response = wp_remote_get($sitemap_url);
        
        if (is_wp_error($response)) {
            // If sitemap.xml doesn't exist, try to get posts directly
            $sitemap_data = $this->analyze_posts_directly();
        } else {
            $sitemap_content = wp_remote_retrieve_body($response);
            
            if (!empty($sitemap_content)) {
                // Parse XML
                libxml_use_internal_errors(true);
                $xml = simplexml_load_string($sitemap_content);
                
                if ($xml !== false) {
                    // Count URLs
                    $urls = $xml->xpath('//url');
                    $sitemap_data['total_urls'] = count($urls);
                    
                    // Analyze content
                    foreach ($urls as $url) {
                        $loc = (string) $url->loc;
                        
                        // Get post ID from URL
                        $post_id = url_to_postid($loc);
                        
                        if ($post_id) {
                            // Get post categories
                            $categories = get_the_category($post_id);
                            
                            foreach ($categories as $category) {
                                if (!isset($sitemap_data['content_categories'][$category->name])) {
                                    $sitemap_data['content_categories'][$category->name] = 0;
                                }
                                
                                $sitemap_data['content_categories'][$category->name]++;
                            }
                            
                            // Get post type
                            $post_type = get_post_type($post_id);
                            
                            if (!isset($sitemap_data['content_types'][$post_type])) {
                                $sitemap_data['content_types'][$post_type] = 0;
                            }
                            
                            $sitemap_data['content_types'][$post_type]++;
                        }
                    }
                } else {
                    // If XML parsing fails, try to get posts directly
                    $sitemap_data = $this->analyze_posts_directly();
                }
            } else {
                // If sitemap is empty, try to get posts directly
                $sitemap_data = $this->analyze_posts_directly();
            }
        }
        
        // Calculate update frequency (posts per month)
        $posts_per_month = $this->calculate_posts_per_month();
        $sitemap_data['update_frequency'] = $posts_per_month;
        
        // Cache for 1 day
        set_transient('aibpg_sitemap_data', $sitemap_data, DAY_IN_SECONDS);
        
        return $sitemap_data;
    }

    /**
     * Analyze posts directly (fallback if sitemap analysis fails)
     *
     * @return array Post analysis data
     */
    private function analyze_posts_directly() {
        $data = array(
            'total_urls' => 0,
            'content_categories' => array(),
            'content_types' => array(),
            'update_frequency' => 0
        );
        
        // Get published posts
        $posts = get_posts(array(
            'post_type' => 'post',
            'post_status' => 'publish',
            'numberposts' => -1
        ));
        
        $data['total_urls'] = count($posts);
        
        foreach ($posts as $post) {
            // Get post categories
            $categories = get_the_category($post->ID);
            
            foreach ($categories as $category) {
                if (!isset($data['content_categories'][$category->name])) {
                    $data['content_categories'][$category->name] = 0;
                }
                
                $data['content_categories'][$category->name]++;
            }
            
            // Get post type
            $post_type = get_post_type($post->ID);
            
            if (!isset($data['content_types'][$post_type])) {
                $data['content_types'][$post_type] = 0;
            }
            
            $data['content_types'][$post_type]++;
        }
        
        return $data;
    }

    /**
     * Calculate posts per month
     *
     * @return float Posts per month
     */
    private function calculate_posts_per_month() {
        // Get posts from the last 3 months
        $three_months_ago = date('Y-m-d H:i:s', strtotime('-3 months'));
        
        $recent_posts = get_posts(array(
            'post_type' => 'post',
            'post_status' => 'publish',
            'date_query' => array(
                'after' => $three_months_ago
            ),
            'numberposts' => -1
        ));
        
        $posts_count = count($recent_posts);
        
        // Calculate posts per month
        $posts_per_month = $posts_count / 3;
        
        return $posts_per_month;
    }

    /**
     * Get existing topics from published posts
     *
     * @return array Existing topics
     */
    private function get_existing_topics() {
        $topics = array();
        
        // Get published posts
        $posts = get_posts(array(
            'post_type' => 'post',
            'post_status' => 'publish',
            'numberposts' => 100 // Limit to last 100 posts for performance
        ));
        
        foreach ($posts as $post) {
            // Get post title
            $title = $post->post_title;
            
            // Get post tags
            $tags = get_the_tags($post->ID);
            
            if ($tags) {
                foreach ($tags as $tag) {
                    $topics[] = $tag->name;
                }
            }
            
            // Get post categories
            $categories = get_the_category($post->ID);
            
            if ($categories) {
                foreach ($categories as $category) {
                    $topics[] = $category->name;
                }
            }
            
            // Add title words (simplified approach)
            $title_words = explode(' ', $title);
            
            foreach ($title_words as $word) {
                if (strlen($word) > 3) {
                    $topics[] = $word;
                }
            }
        }
        
        // Count occurrences
        $topic_counts = array_count_values($topics);
        
        // Sort by frequency
        arsort($topic_counts);
        
        return $topic_counts;
    }

    /**
     * Identify keywords with AI
     *
     * @param string $niche The niche to research
     * @param int $website_authority Website authority score
     * @param array $sitemap_data Sitemap analysis data
     * @param array $existing_topics Existing topics
     * @return array Keywords with data
     */
    private function identify_keywords_with_ai($niche, $website_authority, $sitemap_data, $existing_topics) {
        // Prepare data for AI
        $prompt = $this->prepare_keyword_research_prompt($niche, $website_authority, $sitemap_data, $existing_topics);
        
        // Get available AI services
        $available_services = $this->get_available_ai_services();
        
        if (empty($available_services)) {
            return array();
        }
        
        // Use the first available service
        $service = $available_services[0];
        
        // Generate keywords with AI
        $result = $GLOBALS['ai_blog_post_generator']->api->generate_content($service, $prompt);
        
        if (!$result['success']) {
            return array();
        }
        
        // Parse AI response
        $keywords = $this->parse_keyword_ai_response($result['content']);
        
        return $keywords;
    }

    /**
     * Prepare keyword research prompt for AI
     *
     * @param string $niche The niche to research
     * @param int $website_authority Website authority score
     * @param array $sitemap_data Sitemap analysis data
     * @param array $existing_topics Existing topics
     * @return string Prompt for AI
     */
    private function prepare_keyword_research_prompt($niche, $website_authority, $sitemap_data, $existing_topics) {
        $prompt = "You are an expert SEO keyword researcher. I need you to identify 10 low-competition, high-demand keywords for a website in the {$niche} niche.\n\n";
        
        // Add website authority
        $prompt .= "Website Authority: {$website_authority}/100\n\n";
        
        // Add sitemap data
        $prompt .= "Website Data:\n";
        $prompt .= "- Total URLs: {$sitemap_data['total_urls']}\n";
        $prompt .= "- Content Types: " . implode(', ', array_keys($sitemap_data['content_types'])) . "\n";
        $prompt .= "- Update Frequency: {$sitemap_data['update_frequency']} posts per month\n\n";
        
        // Add top existing topics
        $prompt .= "Top Existing Topics:\n";
        $top_topics = array_slice($existing_topics, 0, 10, true);
        
        foreach ($top_topics as $topic => $count) {
            $prompt .= "- {$topic} ({$count} occurrences)\n";
        }
        
        $prompt .= "\nBased on this data, identify 10 low-competition, high-demand keywords that this website could target to increase organic traffic. For each keyword, provide:\n";
        $prompt .= "1. The keyword\n";
        $prompt .= "2. Monthly search volume (estimate)\n";
        $prompt .= "3. Competition level (low, medium, high)\n";
        $prompt .= "4. Keyword difficulty score (0-100)\n";
        $prompt .= "5. Potential traffic estimate\n";
        $prompt .= "6. Content type recommendation\n";
        $prompt .= "7. A brief explanation of why this keyword is a good target\n\n";
        
        $prompt .= "Format your response as a JSON array with objects for each keyword. Example format:\n";
        $prompt .= '[
  {
    "keyword": "example keyword",
    "search_volume": 1000,
    "competition": "low",
    "difficulty": 25,
    "traffic_potential": 300,
    "content_type": "how-to guide",
    "rationale": "This keyword has low competition but high search volume..."
  }
]';
        
        return $prompt;
    }

    /**
     * Parse AI response for keywords
     *
     * @param string $ai_response The AI response
     * @return array Parsed keywords
     */
    private function parse_keyword_ai_response($ai_response) {
        // Try to extract JSON from the response
        $pattern = '/\[\s*\{.*?\}\s*\]/s';
        preg_match($pattern, $ai_response, $matches);
        
        if (empty($matches)) {
            // If no JSON found, return empty array
            return array();
        }
        
        $json_str = $matches[0];
        
        // Decode JSON
        $keywords = json_decode($json_str, true);
        
        if (json_last_error() !== JSON_ERROR_NONE || !is_array($keywords)) {
            // If JSON decoding fails, return empty array
            return array();
        }
        
        return $keywords;
    }

    /**
     * Get available AI services
     *
     * @return array Available AI services
     */
    private function get_available_ai_services() {
        $services = array();
        
        // Check OpenAI
        $openai_key = get_option('aibpg_openai_api_key', '');
        
        if (!empty($openai_key)) {
            $services[] = 'openai';
        }
        
        // Check Claude
        $claude_key = get_option('aibpg_claude_api_key', '');
        
        if (!empty($claude_key)) {
            $services[] = 'claude';
        }
        
        // Check Gemini
        $gemini_key = get_option('aibpg_gemini_api_key', '');
        
        if (!empty($gemini_key)) {
            $services[] = 'gemini';
        }
        
        // Check OpenRouter
        $openrouter_key = get_option('aibpg_openrouter_api_key', '');
        
        if (!empty($openrouter_key)) {
            $services[] = 'openrouter';
        }
        
        return $services;
    }
}
