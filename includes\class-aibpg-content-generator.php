<?php
/**
 * Content Generator class for AI Blog Post Generator
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

class AIBPG_Content_Generator {
    /**
     * Constructor
     */
    public function __construct() {
        // Nothing to do here for now
    }

    /**
     * Generate a blog post based on keyword
     *
     * @param string $keyword The keyword to target
     * @param string $ai_service The AI service to use (optional)
     * @return array Result with success status, content, title, and message
     */
    public function generate_post($keyword, $ai_service = '') {
        // Validate keyword
        if (empty($keyword)) {
            return array(
                'success' => false,
                'message' => __('Keyword is required for content generation.', 'ai-blog-post-generator')
            );
        }

        // Log the generation attempt
        error_log(sprintf('Generating blog post for keyword: %s using service: %s', $keyword, $ai_service ?: 'default'));

        // If testing mode is enabled, use mock content
        if (defined('AIBPG_TESTING') && AIBPG_TESTING) {
            error_log('Using mock content generator for testing');
            return $this->generate_mock_post($keyword, $ai_service);
        }

        // If no AI service specified, use the default or first available
        if (empty($ai_service)) {
            $ai_service = $this->get_default_ai_service();
            error_log(sprintf('Using default AI service: %s', $ai_service ?: 'none'));
        }

        if (empty($ai_service)) {
            return array(
                'success' => false,
                'message' => __('No AI service available. Please configure an API key in the settings.', 'ai-blog-post-generator')
            );
        }

        // Verify API key is configured
        $api_key = $GLOBALS['ai_blog_post_generator']->api->get_api_key($ai_service);
        if (empty($api_key)) {
            return array(
                'success' => false,
                'message' => sprintf(__('API key for %s is not configured. Please add your API key in the settings.', 'ai-blog-post-generator'), $ai_service)
            );
        }

        try {
            // Research the keyword
            $keyword_data = $this->research_keyword($keyword);

            // Generate the prompt
            $prompt = $this->generate_prompt($keyword, $keyword_data);

            // Generate content with AI
            $result = $GLOBALS['ai_blog_post_generator']->api->generate_content($ai_service, $prompt);

            if (!$result['success']) {
                error_log(sprintf('Content generation failed: %s', $result['message']));
                return $result;
            }

            // Validate the AI response
            if (empty($result['content'])) {
                error_log('AI returned empty content');
                return array(
                    'success' => false,
                    'message' => __('The AI service returned empty content. Please try again.', 'ai-blog-post-generator')
                );
            }

            // Extract title and content
            $extracted = $this->extract_title_and_content($result['content']);

            // Validate extracted content
            if (empty($extracted['title'])) {
                error_log('Failed to extract title from AI response');
                return array(
                    'success' => false,
                    'message' => __('Failed to extract title from AI response. Please try again.', 'ai-blog-post-generator')
                );
            }

            // Add schema markup
            $content_with_schema = $this->add_schema_markup($extracted['title'], $extracted['content'], $keyword);

            // Ensure internal links with advanced relevance matching
            $final_content = $this->ensure_internal_links($content_with_schema, $keyword);

            // Log successful generation
            error_log(sprintf('Successfully generated blog post for keyword: %s (title: %s)', $keyword, $extracted['title']));

            return array(
                'success' => true,
                'title' => $extracted['title'],
                'content' => $final_content,
                'message' => __('Blog post generated successfully.', 'ai-blog-post-generator')
            );
        } catch (Exception $e) {
            error_log(sprintf('Exception during blog post generation: %s', $e->getMessage()));
            return array(
                'success' => false,
                'message' => sprintf(__('An error occurred during content generation: %s', 'ai-blog-post-generator'), $e->getMessage())
            );
        }
    }

    /**
     * Generate a mock blog post for testing
     *
     * @param string $keyword The keyword to target
     * @param string $ai_service The AI service to use
     * @return array Result with success status, content, title, and message
     */
    private function generate_mock_post($keyword, $ai_service = '') {
        // Create a title based on the keyword
        $title = 'The Ultimate Guide to ' . ucwords($keyword);

        // Create internal links for SEO optimization (6 required)
        $internal_links = array(
            array(
                'url' => 'https://gearuptofit.com/fitness-essentials/',
                'anchor' => 'essential fitness equipment',
                'context' => 'Having the right ' . $keyword . ' gear is crucial, just like having the proper <a href="https://gearuptofit.com/fitness-essentials/">essential fitness equipment</a> for any workout routine.'
            ),
            array(
                'url' => 'https://gearuptofit.com/workout-plans/',
                'anchor' => 'structured workout plans',
                'context' => 'Following <a href="https://gearuptofit.com/workout-plans/">structured workout plans</a> that incorporate ' . $keyword . ' can significantly improve your results.'
            ),
            array(
                'url' => 'https://gearuptofit.com/nutrition-guide/',
                'anchor' => 'proper nutrition guidelines',
                'context' => 'For maximum benefits from your ' . $keyword . ' practice, follow our <a href="https://gearuptofit.com/nutrition-guide/">proper nutrition guidelines</a> to fuel your body correctly.'
            ),
            array(
                'url' => 'https://gearuptofit.com/recovery-techniques/',
                'anchor' => 'effective recovery techniques',
                'context' => 'After intense ' . $keyword . ' sessions, implementing <a href="https://gearuptofit.com/recovery-techniques/">effective recovery techniques</a> will help prevent injuries and improve performance.'
            ),
            array(
                'url' => 'https://gearuptofit.com/fitness-tracking/',
                'anchor' => 'progress tracking methods',
                'context' => 'Monitoring your ' . $keyword . ' journey using reliable <a href="https://gearuptofit.com/fitness-tracking/">progress tracking methods</a> helps maintain motivation and identify areas for improvement.'
            ),
            array(
                'url' => 'https://gearuptofit.com/expert-advice/',
                'anchor' => 'expert fitness advice',
                'context' => 'For advanced ' . $keyword . ' practitioners, our <a href="https://gearuptofit.com/expert-advice/">expert fitness advice</a> provides valuable insights to take your practice to the next level.'
            )
        );

        // Create valid external reference links
        $external_links = array(
            array(
                'name' => 'American Council on Exercise',
                'url' => 'https://www.acefitness.org/resources/everyone/blog/'
            ),
            array(
                'name' => 'Journal of Strength and Conditioning Research',
                'url' => 'https://journals.lww.com/nsca-jscr/pages/default.aspx'
            ),
            array(
                'name' => 'National Academy of Sports Medicine',
                'url' => 'https://www.nasm.org/resources'
            ),
            array(
                'name' => 'International Sports Sciences Association',
                'url' => 'https://www.issaonline.com/blog/'
            ),
            array(
                'name' => 'Mayo Clinic Fitness Center',
                'url' => 'https://www.mayoclinic.org/healthy-lifestyle/fitness/basics/fitness-basics/hlv-20049447'
            )
        );

        // Create properly formatted HTML content
        $content = '<h1>' . $title . '</h1>';

        // Engaging introduction
        $content .= '<p>Are you looking to transform your fitness journey with ' . $keyword . '? You\'ve come to the right place. In this comprehensive guide, we\'ll explore everything you need to know about mastering ' . $keyword . ' techniques, benefits, and implementation strategies. Whether you\'re a complete beginner or looking to advance your existing practice, this guide provides actionable insights backed by scientific research and expert experience.</p>';

        // Key takeaways section (immediately after intro)
        $content .= '<h2>Key Takeaways</h2>';
        $content .= '<ul>';
        $content .= '<li>' . ucfirst($keyword) . ' is essential for optimal health and fitness, with studies showing a 45% improvement in overall physical performance</li>';
        $content .= '<li>Regular practice of ' . $keyword . ' can improve your overall wellbeing by reducing stress levels by up to 30%</li>';
        $content .= '<li>Experts recommend incorporating ' . $keyword . ' into your daily routine for at least 20 minutes per session</li>';
        $content .= '<li>The best time for ' . $keyword . ' is in the morning, as research indicates a 20% increase in metabolic benefits</li>';
        $content .= '<li>Quality equipment can enhance your ' . $keyword . ' experience and reduce injury risk by up to 60%</li>';
        $content .= '<li>Consistency is key when developing a ' . $keyword . ' habit—87% of successful practitioners maintain a regular schedule</li>';
        $content .= '</ul>';

        // What is section with internal link #1
        $content .= '<h2>What is ' . ucwords($keyword) . '?</h2>';
        $content .= '<p>' . ucfirst($keyword) . ' refers to a specific set of exercises and techniques designed to improve your physical strength, flexibility, and mental wellbeing. It has gained popularity in recent years due to its numerous health benefits and accessibility. Unlike traditional workout methods, ' . $keyword . ' focuses on holistic development, addressing both physical and mental aspects of fitness.</p>';
        $content .= '<p>' . $internal_links[0]['context'] . '</p>';

        // Benefits section with internal link #2
        $content .= '<h2>Benefits of ' . ucwords($keyword) . '</h2>';
        $content .= '<p>Regular practice of ' . $keyword . ' offers numerous evidence-based benefits:</p>';
        $content .= '<ol>';
        $content .= '<li><strong>Improved Strength</strong>: ' . ucfirst($keyword) . ' helps build lean muscle mass and enhances overall body strength.</li>';
        $content .= '<li><strong>Enhanced Flexibility</strong>: The movements involved in ' . $keyword . ' promote better range of motion and joint flexibility.</li>';
        $content .= '<li><strong>Stress Reduction</strong>: ' . ucfirst($keyword) . ' incorporates mindfulness techniques that help reduce stress and anxiety.</li>';
        $content .= '<li><strong>Better Posture</strong>: Regular practice corrects postural imbalances and promotes proper alignment.</li>';
        $content .= '<li><strong>Increased Energy</strong>: ' . ucfirst($keyword) . ' stimulates blood flow and oxygen delivery, boosting energy levels.</li>';
        $content .= '</ol>';
        $content .= '<p>' . $internal_links[1]['context'] . '</p>';

        // Getting started section with internal link #3
        $content .= '<h2>How to Get Started with ' . ucwords($keyword) . '</h2>';
        $content .= '<p>Beginning your ' . $keyword . ' journey is simple. Here\'s a step-by-step guide:</p>';
        $content .= '<ol>';
        $content .= '<li><strong>Research</strong>: Learn about different ' . $keyword . ' styles and approaches to find what resonates with you.</li>';
        $content .= '<li><strong>Start Small</strong>: Begin with 10-15 minutes daily and gradually increase duration as you build stamina.</li>';
        $content .= '<li><strong>Invest in Quality Equipment</strong>: While not always necessary, proper gear can enhance your ' . $keyword . ' experience.</li>';
        $content .= '<li><strong>Join a Community</strong>: Connect with fellow ' . $keyword . ' enthusiasts for motivation and support.</li>';
        $content .= '<li><strong>Track Progress</strong>: Keep a journal to monitor improvements and stay motivated.</li>';
        $content .= '</ol>';
        $content .= '<p>' . $internal_links[2]['context'] . '</p>';

        // Common mistakes section with internal link #4
        $content .= '<h2>Common Mistakes to Avoid</h2>';
        $content .= '<p>When practicing ' . $keyword . ', be mindful of these common pitfalls:</p>';
        $content .= '<ul>';
        $content .= '<li><strong>Overexertion</strong>: Pushing too hard too quickly can lead to injuries.</li>';
        $content .= '<li><strong>Inconsistency</strong>: Sporadic practice limits the benefits of ' . $keyword . '.</li>';
        $content .= '<li><strong>Poor Form</strong>: Incorrect technique reduces effectiveness and increases injury risk.</li>';
        $content .= '<li><strong>Ignoring Limitations</strong>: Respect your body\'s current capabilities while working to improve.</li>';
        $content .= '<li><strong>Comparison</strong>: Focus on your personal progress rather than comparing yourself to others.</li>';
        $content .= '</ul>';
        $content .= '<p>' . $internal_links[3]['context'] . '</p>';

        // Advanced techniques section with internal link #5
        $content .= '<h2>Advanced ' . ucwords($keyword) . ' Techniques</h2>';
        $content .= '<p>Once you\'ve mastered the basics, consider exploring these advanced ' . $keyword . ' techniques:</p>';
        $content .= '<ol>';
        $content .= '<li><strong>Intensity Variations</strong>: Modify speed and resistance to challenge different muscle groups.</li>';
        $content .= '<li><strong>Combination Movements</strong>: Integrate complementary exercises for comprehensive workouts.</li>';
        $content .= '<li><strong>Extended Duration</strong>: Gradually increase session length for enhanced endurance.</li>';
        $content .= '<li><strong>Specialized Equipment</strong>: Explore tools designed specifically for advanced ' . $keyword . ' practitioners.</li>';
        $content .= '<li><strong>Competitive ' . ucwords($keyword) . '</strong>: Consider participating in ' . $keyword . ' competitions or challenges.</li>';
        $content .= '</ol>';
        $content .= '<p>' . $internal_links[4]['context'] . '</p>';

        // FAQ section with internal link #6
        $content .= '<h2>Frequently Asked Questions</h2>';
        $content .= '<h3>How often should I practice ' . $keyword . '?</h3>';
        $content .= '<p>For optimal results, aim for 3-5 sessions per week, allowing for recovery days between intense workouts.</p>';

        $content .= '<h3>Can ' . $keyword . ' help with weight loss?</h3>';
        $content .= '<p>Yes, ' . $keyword . ' can be an effective component of a weight loss program when combined with proper nutrition and consistent practice.</p>';

        $content .= '<h3>Is ' . $keyword . ' suitable for beginners?</h3>';
        $content .= '<p>Absolutely! ' . ucfirst($keyword) . ' can be modified to accommodate all fitness levels, making it accessible to beginners.</p>';

        $content .= '<h3>What\'s the best time of day for ' . $keyword . '?</h3>';
        $content .= '<p>While morning sessions often provide energy for the day ahead, the best time is whenever you can consistently practice.</p>';

        $content .= '<h3>How long until I see results from ' . $keyword . '?</h3>';
        $content .= '<p>Most practitioners notice improvements in flexibility and energy within 2-3 weeks, with more significant changes appearing after 8-12 weeks of regular practice. ' . $internal_links[5]['context'] . '</p>';

        // Conclusion
        $content .= '<h2>Conclusion</h2>';
        $content .= '<p>' . ucfirst($keyword) . ' offers a transformative approach to fitness that benefits both body and mind. By incorporating these practices into your routine and avoiding common mistakes, you\'ll experience improved strength, flexibility, and overall wellbeing. Remember that consistency is key—start where you are, use what you have, and do what you can. Your ' . $keyword . ' journey is unique, and every step forward is progress worth celebrating.</p>';

        // References with valid external links
        $content .= '<h2>References</h2>';
        $content .= '<ul>';
        foreach ($external_links as $link) {
            $content .= '<li><a href="' . $link['url'] . '" target="_blank" rel="noopener noreferrer">' . $link['name'] . '</a></li>';
        }
        $content .= '</ul>';

        // Add schema markup for SEO
        $schema = array(
            '@context' => 'https://schema.org',
            '@type' => 'Article',
            'headline' => $title,
            'description' => 'Complete guide to ' . $keyword . ' with expert tips, techniques, and benefits',
            'keywords' => $keyword . ', fitness, guide, techniques, benefits',
            'author' => array(
                '@type' => 'Person',
                'name' => 'Fitness Expert'
            ),
            'publisher' => array(
                '@type' => 'Organization',
                'name' => 'GearUpToFit',
                'logo' => array(
                    '@type' => 'ImageObject',
                    'url' => 'https://gearuptofit.com/logo.png'
                )
            ),
            'datePublished' => date('c'),
            'dateModified' => date('c')
        );

        $schema_script = '<script type="application/ld+json">' . json_encode($schema, JSON_UNESCAPED_SLASHES | JSON_PRETTY_PRINT) . '</script>';
        $content = $schema_script . $content;

        // Log successful mock generation
        error_log(sprintf('Successfully generated high-quality blog post for keyword: %s (title: %s)', $keyword, $title));

        return array(
            'success' => true,
            'title' => $title,
            'content' => $content,
            'message' => __('High-quality blog post generated successfully with proper formatting, internal links, and valid references.', 'ai-blog-post-generator')
        );
    }

    /**
     * Get default AI service
     *
     * @return string Default AI service
     */
    private function get_default_ai_service() {
        // Check if default is set
        $default = get_option('aibpg_default_ai_service', '');

        if (!empty($default)) {
            $api_key = get_option('aibpg_' . $default . '_api_key', '');

            if (!empty($api_key)) {
                return $default;
            }
        }

        // If no default or default has no API key, use first available
        $services = array('openai', 'claude', 'gemini', 'openrouter');

        foreach ($services as $service) {
            $api_key = get_option('aibpg_' . $service . '_api_key', '');

            if (!empty($api_key)) {
                return $service;
            }
        }

        return '';
    }

    /**
     * Research a specific keyword
     *
     * @param string $keyword The keyword to research
     * @return array Keyword data
     */
    private function research_keyword($keyword) {
        // In a real implementation, this would connect to an SEO API
        // For now, we'll use a placeholder

        // Check if we have cached data for this keyword
        $transient_key = 'aibpg_keyword_' . md5($keyword);
        $cached_data = get_transient($transient_key);

        if (false !== $cached_data) {
            return $cached_data;
        }

        // Simulate keyword research
        $data = array(
            'search_volume' => rand(500, 5000),
            'competition' => array('low', 'medium', 'high')[rand(0, 2)],
            'difficulty' => rand(10, 70),
            'related_keywords' => $this->generate_related_keywords($keyword),
            'questions' => $this->generate_questions($keyword)
        );

        // Cache for 7 days
        set_transient($transient_key, $data, 7 * DAY_IN_SECONDS);

        return $data;
    }

    /**
     * Generate related keywords (placeholder)
     *
     * @param string $keyword The main keyword
     * @return array Related keywords
     */
    private function generate_related_keywords($keyword) {
        // In a real implementation, this would use an SEO API
        // For now, we'll generate some placeholder related keywords
        $words = explode(' ', $keyword);
        $related = array();

        // Generate variations
        $related[] = 'best ' . $keyword;
        $related[] = $keyword . ' guide';
        $related[] = 'how to ' . $keyword;
        $related[] = $keyword . ' tips';
        $related[] = $keyword . ' for beginners';

        if (count($words) > 1) {
            // Rearrange words
            $shuffled = $words;
            shuffle($shuffled);
            $related[] = implode(' ', $shuffled);

            // Add words
            $related[] = $keyword . ' techniques';
            $related[] = 'advanced ' . $keyword;
        }

        return $related;
    }

    /**
     * Generate questions (placeholder)
     *
     * @param string $keyword The main keyword
     * @return array Questions
     */
    private function generate_questions($keyword) {
        // In a real implementation, this would use an SEO API or AI
        // For now, we'll generate some placeholder questions
        $questions = array(
            'What is ' . $keyword . '?',
            'How does ' . $keyword . ' work?',
            'Why is ' . $keyword . ' important?',
            'When should you use ' . $keyword . '?',
            'Who needs ' . $keyword . '?'
        );

        return $questions;
    }

    /**
     * Generate prompt for AI
     *
     * @param string $keyword The keyword to target
     * @param array $keyword_data Keyword research data
     * @return string Prompt for AI
     */
    private function generate_prompt($keyword, $keyword_data) {
        // Get internal links
        $internal_links = $this->get_internal_links();

        $prompt = "You are an expert AI Content Strategist and Writer, specializing in creating deeply human, engaging, and SEO-optimized content for the health and fitness niche. Your mission is to craft blog posts that rank #1 on Google rapidly (<10 days) by being the definitive, most helpful, and most readable resource on the topic, while subtly driving affiliate conversions through value and trust.

Generate a 2000-2500 word SEO-optimized blog post based on your in-depth research on the keyword \"{$keyword}\". The blog should be well-structured, engaging, and highly informative, written from a health and fitness expert perspective, and tailored to the keyword.

Your Persona & Voice:
- Expert Friend: Act as a knowledgeable, experienced, and approachable expert in the health and fitness niche. You're sharing genuine insights, personal experiences (real or plausibly synthesized), and critical thinking directly with the reader.
- Conversational & Engaging: Write in a casual, smart, conversational tone. Think 'trusted friend explaining something fascinating over coffee,' not a formal lecture or a robotic script.
- Observational & Descriptive (Subtle Bukowski/Time Influence): Paint a vivid picture of the topic. Focus on insightful observation rather than hard-selling. Use a slightly gritty, realistic lens where appropriate, but maintain an overall positive and helpful tone. Avoid hype and marketing clichés. Be opinionated based on expertise and critical analysis.
- Authentic & Relatable: Weave in [PERSONAL EXPERIENCE] (use hypotheticals if needed, make them sound real) and relatable analogies or brief anecdotes to illustrate points and connect with the reader. Avoid cringe-worthy or overly simplistic analogies (like 'party for your feet').

Write in the style of Charles Bukowski working for Time Magazine and with a tongue in cheek twinge, but without overdoing anything - avoiding completely any kind of selling or marketing language and instead painting a descriptive picture of the article. You are not trying to SELL anything you are just OBSERVING. Vary the length of sentences, use perplexity and burstiness to increase randomness of your sentences. Use the active voice. Do not write sentences with comparisons inside it, and do not write descriptive, contrasting ideals, and uses an em dash for emphasis.

Write using words a 7 year old would understand - but don't make cringe analogies such as:
- Premiata sneakers are like a party for your feet!
- Premiata sneakers are like a rainbow for your feet, with lots of color!

Your mission is to create high-converting, search-optimized content for gearuptofit.com that achieves rapid rankings through strategic optimization. Your task is to craft an article targeting the keyword \"{$keyword}\". This article must rank #1 in Google search results and be the definitive resource on the subject.

Start with a highly interested, high value, SEO optimized intro, including the main keyword and answering directly the main subject!!!

After the intro create a highly helpful section with h3 [Key Takeaways] with 6 very concise and helpful bullets about the main keyword in a concise yet impactful manner!!!!

Related Keywords to Include: " . implode(', ', $keyword_data['related_keywords']) . "

Questions to Answer:
" . implode("\n", $keyword_data['questions']) . "

Internal Links to Include (use these exact URLs with appropriate anchor text - MUST include exactly 6 internal links with rich anchor text, strategically placed throughout the content):
";

        foreach ($internal_links as $url => $title) {
            $prompt .= "- {$url} ({$title})\n";
        }

        $prompt .= "
Content Foundation
Core Parameters:
Domain: gearuptofit.com
Purpose: Affiliate conversion + SEO ranking
Time to Rank Target: < 10 days
Word Count: 2,000-2,500 words
Target Reader: health and fitness enthusiasts

Strategic Requirements
1. Search Intent Optimization
- Direct answer within first 100 words
- Use BLUF (Bottom Line Up Front) approach
- Structure for [People Also Ask] opportunities with clear H2/H3 headings
- Target comparison and buyer-intent keywords
- Include a FAQ section with at least 5 questions and answers
- Use the main keyword in the first paragraph, first H2, and conclusion

2. Content Architecture
- Create topic clusters around main keyword
- Use QRIES framework (Quotes, Research, Images, Examples, Statistics)
- Implement schema-ready formatting for rich snippets

3. SEO Enhancement
- Include all LSI keywords strategically
- Include additional highly relevant LSI keywords naturally
- Create content hubs with internal linking
- Optimize for featured snippets with clear headers

4. Conversion Elements
- Add product comparisons and clear CTAs
- Create product tables and buying guides

5. Technical Optimization
- Use dynamic parameters for efficient crawling
- Implement proper header hierarchy
- Optimize meta descriptions and title tags

Content Generation Rules
- Opening Impact: Start with a direct answer, key stats or proof points.
- Body Structure: Use short paragraphs, practical examples, and comparison tables.
- Closing Elements: Include a clear CTA, summary of key points, and related content suggestions.

Quality Markers
- Original research or data when possible
- Expert insights and analysis
- Practical, actionable advice

I. Strategic Foundation:
a. Analyze search intent behind the keyword. Align the article with this intent.
b. Evaluate top 5 ranking pages for the keyword and surpass their strengths and weaknesses.
c. Structure as a content pillar to facilitate linking to related subtopics.
d. Write to facilitate schema markup application.

II. Content Creation & Optimization:
a. Answer primary search query in the first paragraph.
b. Provide comprehensive coverage of the keyword
c. Deliver actionable guidance with checklists.
d. Integrate LSI keywords naturally.

III. Call-to-Action and Next Steps:
a. Include a strong CTA at the end and strategically throughout.
b. Incorporate internal and external linking strategies.

IV. Parameters:
a. Tailor language and tone to target audience.
b. Aim for around 2500 words without unnecessary introductions.

Structure & Formatting
- Use a natural, conversational, and relaxed tone while maintaining professionalism.
- Organize the content using proper headers:
  - H1 for the title
  - H2s for main sections
  - H3s for sub-sections
- Ensure clear, logical progression between sections to maintain readability.

SEO Optimization
- Seamlessly incorporate relevant keywords throughout the content for better search ranking.
- IMPORTANT 🚨 Ensure all url research is properly hyperlinked within the content (not listed at the end), using contextually relevant anchor text.
- Optimize headers, meta descriptions, and paragraph structures to enhance SEO value.

Research & Supporting Content
- Integrate credible sources by hyperlinking them within relevant sentences.
- Where appropriate, include one or two well-designed tables to present data, comparisons, or summaries in a visually engaging way.

Content Flow
1. Introduction
- Start with a captivating hook to engage the reader.
- Briefly introduce the topic's relevance and what the post will cover.

2. Main Body
- Break the topic into well-defined sections, ensuring clarity and depth.
- Provide detailed analysis, actionable insights, and expert explanations.
- Where relevant, weave in personal experiences to add authenticity.

3. Conclusion
- Summarize the key takeaways in a concise yet impactful manner.
- End with a call-to-action, guiding the reader on the next steps (e.g., further reading, discussion, or application of insights).

At the end, create an h2 section about [References] with the most helpful and valuable but 100% accurate and fully functional urls for references!

Use markdown format to make the format perfect for a reading experience!!

Format your response with the title as the first line, followed by the blog post content.";

        return $prompt;
    }

    /**
     * Get internal links for inclusion in the blog post
     *
     * @param int $count Number of links to get (default: 6)
     * @return array Internal links (URL => Title)
     */
    private function get_internal_links($count = 6) {
        $links = array();

        // Get published posts
        $posts = get_posts(array(
            'post_type' => 'post',
            'post_status' => 'publish',
            'numberposts' => $count,
            'orderby' => 'rand'
        ));

        if (empty($posts)) {
            // If no posts found, return empty array
            return $links;
        }

        foreach ($posts as $post) {
            $links[get_permalink($post->ID)] = $post->post_title;

            if (count($links) >= $count) {
                break;
            }
        }

        return $links;
    }

    /**
     * Extract title and content from AI response
     *
     * @param string $ai_response The AI response
     * @return array Title and content
     */
    private function extract_title_and_content($ai_response) {
        // Split by newline
        $lines = explode("\n", $ai_response);

        // First non-empty line is the title
        $title = '';

        foreach ($lines as $index => $line) {
            $line = trim($line);

            if (!empty($line)) {
                $title = $line;

                // Remove markdown formatting from title
                $title = preg_replace('/^#+ /', '', $title);

                // Remove the title line from the content
                unset($lines[$index]);
                break;
            }
        }

        // Remaining lines are the content
        $content = implode("\n", $lines);

        return array(
            'title' => $title,
            'content' => $content
        );
    }

    /**
     * Add schema markup to the content
     *
     * @param string $title The post title
     * @param string $content The post content
     * @param string $keyword The target keyword
     * @return string Content with schema markup
     */
    private function add_schema_markup($title, $content, $keyword) {
        // Extract the first paragraph as description
        $description = '';
        $paragraphs = explode("\n\n", $content);

        foreach ($paragraphs as $paragraph) {
            $paragraph = trim($paragraph);
            if (!empty($paragraph) && strpos($paragraph, '#') !== 0) {
                $description = wp_strip_all_tags($paragraph);
                if (strlen($description) > 50) {
                    break;
                }
            }
        }

        // Limit description length
        if (strlen($description) > 160) {
            $description = substr($description, 0, 157) . '...';
        }

        // Create schema markup
        $schema = array(
            '@context' => 'https://schema.org',
            '@type' => 'Article',
            'headline' => $title,
            'description' => $description,
            'keywords' => $keyword,
            'author' => array(
                '@type' => 'Person',
                'name' => get_the_author_meta('display_name', get_current_user_id())
            ),
            'publisher' => array(
                '@type' => 'Organization',
                'name' => get_bloginfo('name'),
                'logo' => array(
                    '@type' => 'ImageObject',
                    'url' => get_site_icon_url()
                )
            ),
            'datePublished' => date('c'),
            'dateModified' => date('c')
        );

        // Add schema markup to the content
        $schema_script = '<script type="application/ld+json">' . json_encode($schema, JSON_UNESCAPED_SLASHES | JSON_PRETTY_PRINT) . '</script>';

        // Add schema markup at the beginning of the content
        $content = $schema_script . "\n\n" . $content;

        return $content;
    }

    /**
     * Ensure the content has at least 6 internal links
     *
     * @param string $content The post content
     * @return string Content with internal links
     */
    private function ensure_internal_links($content, $keyword = '') {
        // Count existing internal links
        $internal_link_count = substr_count($content, site_url());

        // Get the target number of internal links from settings
        $target_links = intval(get_option('aibpg_internal_links_count', 6));

        // If we already have enough internal links, return the content as is
        if ($internal_link_count >= $target_links) {
            return $content;
        }

        // Initialize the internal links manager
        $internal_links_manager = new AIBPG_Internal_Links();

        // Get optimal internal links based on content relevance and site structure
        $optimal_links = $internal_links_manager->get_optimal_internal_links($content, $keyword, $target_links);

        // If we don't have enough internal links in our database, fall back to the old method
        if (empty($optimal_links)) {
            // Get internal links using the old method
            $internal_links = $this->get_internal_links();

            // If we still don't have enough links, return the content as is
            if (count($internal_links) < ($target_links - $internal_link_count)) {
                return $content;
            }

            // Shuffle the internal links
            $keys = array_keys($internal_links);
            shuffle($keys);
            $optimal_links = array();
            foreach ($keys as $key) {
                $optimal_links[$key] = $internal_links[$key];
            }
        }

        // Add internal links to the content
        $links_to_add = $target_links - $internal_link_count;
        $links_added = 0;
        $paragraphs = explode("\n\n", $content);

        // Track which links we've already used
        $used_links = array();

        // Start from the second paragraph to avoid adding links to the schema markup
        for ($i = 1; $i < count($paragraphs); $i++) {
            // Skip headers and short paragraphs
            if (strpos($paragraphs[$i], '#') === 0 || strlen($paragraphs[$i]) < 100) {
                continue;
            }

            // Skip paragraphs that already have links
            if (strpos($paragraphs[$i], 'href=') !== false) {
                continue;
            }

            // Find the most relevant link for this paragraph
            $best_link_url = null;
            $best_link_title = null;

            foreach ($optimal_links as $url => $title) {
                // Skip links we've already used
                if (isset($used_links[$url])) {
                    continue;
                }

                // Found a link to use
                $best_link_url = $url;
                $best_link_title = $title;
                break;
            }

            // If we found a link to use
            if ($best_link_url) {
                // Create a link with rich anchor text
                $link_html = '<a href="' . esc_url($best_link_url) . '">' . esc_html($best_link_title) . '</a>';

                // Add the link to the paragraph
                $words = explode(' ', $paragraphs[$i]);

                // Find a suitable position (around 1/3 into the paragraph)
                $position = intval(count($words) / 3);
                if ($position < 3) {
                    $position = 3;
                }

                // Insert the link
                array_splice($words, $position, 0, $link_html);
                $paragraphs[$i] = implode(' ', $words);

                // Mark this link as used
                $used_links[$best_link_url] = true;

                $links_added++;

                // If we've added enough links, break
                if ($links_added >= $links_to_add) {
                    break;
                }
            }
        }

        // Reassemble the content
        $content = implode("\n\n", $paragraphs);

        return $content;
    }

    /**
     * Find potential anchor text in content for a given title
     *
     * @param string $content The content to analyze
     * @param string $title The post title to match
     * @return array Potential anchor text with relevance scores
     */
    private function find_potential_anchor_text($content, $title) {
        $potential_anchors = array();

        // Try with full title
        if (stripos($content, $title) !== false) {
            $potential_anchors[$title] = 10; // Highest score for exact match
        }

        // Try with title parts
        $words = explode(' ', $title);

        // Try with 3-word combinations
        if (count($words) >= 3) {
            for ($i = 0; $i <= count($words) - 3; $i++) {
                $phrase = $words[$i] . ' ' . $words[$i + 1] . ' ' . $words[$i + 2];

                if (stripos($content, $phrase) !== false) {
                    $potential_anchors[$phrase] = 8; // High score for 3-word match
                }
            }
        }

        // Try with 2-word combinations
        if (count($words) >= 2) {
            for ($i = 0; $i <= count($words) - 2; $i++) {
                $phrase = $words[$i] . ' ' . $words[$i + 1];

                if (stripos($content, $phrase) !== false) {
                    $potential_anchors[$phrase] = 6; // Medium score for 2-word match
                }
            }
        }

        // Try with single words (longer than 4 characters)
        foreach ($words as $word) {
            if (strlen($word) > 4 && stripos($content, $word) !== false) {
                $potential_anchors[$word] = 4; // Lower score for single word match
            }
        }

        return $potential_anchors;
    }
}
