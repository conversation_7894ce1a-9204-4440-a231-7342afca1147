<div class="wrap">
    <h1><?php echo esc_html__('AI Blog Post Generator Settings', 'ai-blog-post-generator'); ?></h1>

    <div class="aibpg-settings">
        <h2 class="nav-tab-wrapper">
            <a href="#api-keys" class="nav-tab nav-tab-active"><?php echo esc_html__('API Keys', 'ai-blog-post-generator'); ?></a>
            <a href="#content-settings" class="nav-tab"><?php echo esc_html__('Content Settings', 'ai-blog-post-generator'); ?></a>
        </h2>

        <div class="aibpg-tab-content" id="api-keys-content">
            <form method="post" action="options.php" id="api-keys-form">
                <?php settings_fields('aibpg_api_settings'); ?>

                <table class="form-table">
                    <tr>
                        <th scope="row">
                            <label for="aibpg_openai_api_key"><?php echo esc_html__('OpenAI API Key', 'ai-blog-post-generator'); ?></label>
                        </th>
                        <td>
                            <input type="password" name="aibpg_openai_api_key" id="aibpg_openai_api_key" value="<?php echo esc_attr(get_option('aibpg_openai_api_key', '')); ?>" class="regular-text">
                            <button type="button" class="button button-secondary aibpg-validate-key" data-service="openai"><?php echo esc_html__('Validate', 'ai-blog-post-generator'); ?></button>
                            <p class="description"><?php echo esc_html__('Enter your OpenAI API key. Get one at https://platform.openai.com/', 'ai-blog-post-generator'); ?></p>
                            <div class="aibpg-validation-result" id="openai-validation-result"></div>
                        </td>
                    </tr>

                    <tr>
                        <th scope="row">
                            <label for="aibpg_claude_api_key"><?php echo esc_html__('Claude API Key', 'ai-blog-post-generator'); ?></label>
                        </th>
                        <td>
                            <input type="password" name="aibpg_claude_api_key" id="aibpg_claude_api_key" value="<?php echo esc_attr(get_option('aibpg_claude_api_key', '')); ?>" class="regular-text">
                            <button type="button" class="button button-secondary aibpg-validate-key" data-service="claude"><?php echo esc_html__('Validate', 'ai-blog-post-generator'); ?></button>
                            <p class="description"><?php echo esc_html__('Enter your Claude API key. Get one at https://console.anthropic.com/', 'ai-blog-post-generator'); ?></p>
                            <div class="aibpg-validation-result" id="claude-validation-result"></div>
                        </td>
                    </tr>

                    <tr>
                        <th scope="row">
                            <label for="aibpg_gemini_api_key"><?php echo esc_html__('Gemini API Key', 'ai-blog-post-generator'); ?></label>
                        </th>
                        <td>
                            <input type="password" name="aibpg_gemini_api_key" id="aibpg_gemini_api_key" value="<?php echo esc_attr(get_option('aibpg_gemini_api_key', '')); ?>" class="regular-text">
                            <button type="button" class="button button-secondary aibpg-validate-key" data-service="gemini"><?php echo esc_html__('Validate', 'ai-blog-post-generator'); ?></button>
                            <p class="description"><?php echo esc_html__('Enter your Gemini API key. Get one at https://ai.google.dev/', 'ai-blog-post-generator'); ?></p>
                            <div class="aibpg-validation-result" id="gemini-validation-result"></div>
                        </td>
                    </tr>

                    <tr>
                        <th scope="row">
                            <label for="aibpg_gemini_preferred_model"><?php echo esc_html__('Gemini Preferred Model', 'ai-blog-post-generator'); ?></label>
                        </th>
                        <td>
                            <select name="aibpg_gemini_preferred_model" id="aibpg_gemini_preferred_model" <?php echo empty(get_option('aibpg_gemini_api_key', '')) ? 'disabled' : ''; ?>>
                                <option value=""><?php echo esc_html__('Select a model', 'ai-blog-post-generator'); ?></option>
                                <?php
                                $preferred_model = get_option('aibpg_gemini_preferred_model', '');
                                $gemini_models = $GLOBALS['ai_blog_post_generator']->api->get_available_models('gemini');

                                if (!empty($gemini_models)) {
                                    foreach ($gemini_models as $model_id => $model_name) {
                                        $selected = selected($preferred_model, $model_id, false);
                                        echo '<option value="' . esc_attr($model_id) . '" ' . $selected . '>' . esc_html($model_name) . '</option>';
                                    }
                                }
                                ?>
                            </select>
                            <p class="description"><?php echo esc_html__('Select your preferred model for Gemini. Validate your API key to see available models.', 'ai-blog-post-generator'); ?></p>
                        </td>
                    </tr>

                    <tr>
                        <th scope="row">
                            <label for="aibpg_openrouter_api_key"><?php echo esc_html__('OpenRouter API Key', 'ai-blog-post-generator'); ?></label>
                        </th>
                        <td>
                            <input type="password" name="aibpg_openrouter_api_key" id="aibpg_openrouter_api_key" value="<?php echo esc_attr(get_option('aibpg_openrouter_api_key', '')); ?>" class="regular-text aibpg-api-key-field">
                            <button type="button" class="button button-secondary aibpg-validate-key" data-service="openrouter"><?php echo esc_html__('Validate', 'ai-blog-post-generator'); ?></button>
                            <p class="description"><?php echo esc_html__('Enter your OpenRouter API key. Get one at https://openrouter.ai/', 'ai-blog-post-generator'); ?></p>
                            <div class="aibpg-validation-result" id="openrouter-validation-result"></div>
                        </td>
                    </tr>

                    <tr>
                        <th scope="row">
                            <label for="aibpg_openrouter_preferred_model"><?php echo esc_html__('OpenRouter Preferred Model', 'ai-blog-post-generator'); ?></label>
                        </th>
                        <td>
                            <select name="aibpg_openrouter_preferred_model" id="aibpg_openrouter_preferred_model" <?php echo empty(get_option('aibpg_openrouter_api_key', '')) ? 'disabled' : ''; ?>>
                                <option value=""><?php echo esc_html__('Select a model', 'ai-blog-post-generator'); ?></option>
                                <?php
                                $preferred_model = get_option('aibpg_openrouter_preferred_model', '');
                                $openrouter_models = $GLOBALS['ai_blog_post_generator']->api->get_available_models('openrouter');

                                if (!empty($openrouter_models)) {
                                    foreach ($openrouter_models as $model_id => $model_name) {
                                        $selected = selected($preferred_model, $model_id, false);
                                        echo '<option value="' . esc_attr($model_id) . '" ' . $selected . '>' . esc_html($model_name) . '</option>';
                                    }
                                }
                                ?>
                            </select>
                            <p class="description"><?php echo esc_html__('Select your preferred model for OpenRouter. Validate your API key to see available models.', 'ai-blog-post-generator'); ?></p>
                        </td>
                    </tr>
                </table>

                <?php submit_button(); ?>
            </form>
        </div>

        <div class="aibpg-tab-content" id="content-settings-content" style="display: none;">
            <form method="post" action="options.php" id="content-settings-form">
                <?php settings_fields('aibpg_content_settings'); ?>

                <table class="form-table">
                    <tr>
                        <th scope="row">
                            <label for="aibpg_default_ai_service"><?php echo esc_html__('Default AI Service', 'ai-blog-post-generator'); ?></label>
                        </th>
                        <td>
                            <select name="aibpg_default_ai_service" id="aibpg_default_ai_service">
                                <option value=""><?php echo esc_html__('Auto (use first available)', 'ai-blog-post-generator'); ?></option>
                                <?php
                                $default_service = get_option('aibpg_default_ai_service', '');
                                $services = array(
                                    'openai' => __('OpenAI', 'ai-blog-post-generator'),
                                    'claude' => __('Claude', 'ai-blog-post-generator'),
                                    'gemini' => __('Gemini', 'ai-blog-post-generator'),
                                    'openrouter' => __('OpenRouter', 'ai-blog-post-generator')
                                );

                                foreach ($services as $value => $label) {
                                    $selected = selected($default_service, $value, false);
                                    echo '<option value="' . esc_attr($value) . '" ' . $selected . '>' . esc_html($label) . '</option>';
                                }
                                ?>
                            </select>
                            <p class="description"><?php echo esc_html__('Select the default AI service to use for content generation.', 'ai-blog-post-generator'); ?></p>
                        </td>
                    </tr>

                    <tr>
                        <th scope="row">
                            <label for="aibpg_post_length"><?php echo esc_html__('Post Length', 'ai-blog-post-generator'); ?></label>
                        </th>
                        <td>
                            <select name="aibpg_post_length" id="aibpg_post_length">
                                <?php
                                $post_length = get_option('aibpg_post_length', '2000');
                                $lengths = array(
                                    '1000' => __('Short (~1000 words)', 'ai-blog-post-generator'),
                                    '2000' => __('Medium (~2000 words)', 'ai-blog-post-generator'),
                                    '3000' => __('Long (~3000 words)', 'ai-blog-post-generator')
                                );

                                foreach ($lengths as $value => $label) {
                                    $selected = selected($post_length, $value, false);
                                    echo '<option value="' . esc_attr($value) . '" ' . $selected . '>' . esc_html($label) . '</option>';
                                }
                                ?>
                            </select>
                            <p class="description"><?php echo esc_html__('Select the default length for generated blog posts.', 'ai-blog-post-generator'); ?></p>
                        </td>
                    </tr>

                    <tr>
                        <th scope="row">
                            <label for="aibpg_internal_links_count"><?php echo esc_html__('Internal Links', 'ai-blog-post-generator'); ?></label>
                        </th>
                        <td>
                            <input type="number" name="aibpg_internal_links_count" id="aibpg_internal_links_count" value="<?php echo esc_attr(get_option('aibpg_internal_links_count', 6)); ?>" min="0" max="20" class="small-text">
                            <p class="description"><?php echo esc_html__('Number of internal links to add to each generated post.', 'ai-blog-post-generator'); ?></p>
                        </td>
                    </tr>

                    <tr>
                        <th scope="row">
                            <label for="aibpg_add_faq_schema"><?php echo esc_html__('FAQ Schema', 'ai-blog-post-generator'); ?></label>
                        </th>
                        <td>
                            <label>
                                <input type="checkbox" name="aibpg_add_faq_schema" id="aibpg_add_faq_schema" value="1" <?php checked(get_option('aibpg_add_faq_schema', 1), 1); ?>>
                                <?php echo esc_html__('Automatically add FAQ schema to generated posts', 'ai-blog-post-generator'); ?>
                            </label>
                            <p class="description"><?php echo esc_html__('Extracts questions and answers from the content to create FAQ schema markup.', 'ai-blog-post-generator'); ?></p>
                        </td>
                    </tr>

                    <tr>
                        <th scope="row">
                            <label for="aibpg_add_toc"><?php echo esc_html__('Table of Contents', 'ai-blog-post-generator'); ?></label>
                        </th>
                        <td>
                            <label>
                                <input type="checkbox" name="aibpg_add_toc" id="aibpg_add_toc" value="1" <?php checked(get_option('aibpg_add_toc', 1), 1); ?>>
                                <?php echo esc_html__('Add table of contents to generated posts', 'ai-blog-post-generator'); ?>
                            </label>
                            <p class="description"><?php echo esc_html__('Creates a clickable table of contents based on headings.', 'ai-blog-post-generator'); ?></p>
                        </td>
                    </tr>

                    <tr>
                        <th scope="row">
                            <label for="aibpg_sitemap_urls"><?php echo esc_html__('Sitemap URLs', 'ai-blog-post-generator'); ?></label>
                        </th>
                        <td>
                            <textarea name="aibpg_sitemap_urls" id="aibpg_sitemap_urls" rows="5" class="large-text"><?php echo esc_textarea(get_option('aibpg_sitemap_urls', '')); ?></textarea>
                            <p class="description"><?php echo esc_html__('Enter additional sitemap URLs to analyze for internal linking (one per line).', 'ai-blog-post-generator'); ?></p>
                            <button type="button" id="aibpg-analyze-sitemaps" class="button button-secondary"><?php echo esc_html__('Analyze Sitemaps', 'ai-blog-post-generator'); ?></button>
                            <span id="aibpg-sitemap-analysis-status"></span>
                        </td>
                    </tr>
                </table>

                <?php submit_button(); ?>
            </form>
        </div>
    </div>
</div>
