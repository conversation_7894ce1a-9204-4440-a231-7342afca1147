<?php
/**
 * Plugin Name: AI Blog Post Generator
 * Plugin URI: https://example.com/ai-blog-post-generator
 * Description: Automatically generates SEO-optimized blog posts using AI to boost organic traffic.
 * Version: 1.0.0
 * Author: Augment Agent
 * Author URI: https://example.com
 * Text Domain: ai-blog-post-generator
 * Domain Path: /languages
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

// Define testing mode constant
define('AIBPG_TESTING', true);

// Define plugin constants
define('AIBPG_VERSION', '1.0.0');
define('AIBPG_PLUGIN_DIR', dirname(__FILE__) . '/');
define('AIBPG_PLUGIN_URL', 'http://localhost/wp-content/plugins/ai-blog-post-generator/');
define('AIBPG_PLUGIN_BASENAME', basename(dirname(__FILE__)) . '/' . basename(__FILE__));

// Include required files
require_once AIBPG_PLUGIN_DIR . 'includes/class-aibpg-admin.php';
require_once AIBPG_PLUGIN_DIR . 'includes/class-aibpg-api.php';
require_once AIBPG_PLUGIN_DIR . 'includes/class-aibpg-keyword-research.php';
require_once AIBPG_PLUGIN_DIR . 'includes/class-aibpg-content-generator.php';
require_once AIBPG_PLUGIN_DIR . 'includes/class-aibpg-formatter.php';
require_once AIBPG_PLUGIN_DIR . 'includes/class-aibpg-internal-links.php';
require_once AIBPG_PLUGIN_DIR . 'includes/class-aibpg-sitemap-analyzer.php';
require_once AIBPG_PLUGIN_DIR . 'includes/lib/Parsedown.php';

/**
 * Main plugin class
 */
class AI_Blog_Post_Generator {
    /**
     * Instance of this class
     */
    private static $instance = null;

    /**
     * Admin class instance
     */
    public $admin;

    /**
     * API class instance
     */
    public $api;

    /**
     * Keyword research class instance
     */
    public $keyword_research;

    /**
     * Content generator class instance
     */
    public $content_generator;

    /**
     * Formatter class instance
     */
    public $formatter;

    /**
     * Get the singleton instance
     */
    public static function get_instance() {
        if (is_null(self::$instance)) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Constructor
     */
    private function __construct() {
        // Initialize plugin components
        $this->init();

        // Register activation and deactivation hooks
        register_activation_hook(__FILE__, array($this, 'activate'));
        register_deactivation_hook(__FILE__, array($this, 'deactivate'));
    }

    /**
     * Initialize plugin components
     */
    private function init() {
        // Initialize admin
        $this->admin = new AIBPG_Admin();

        // Initialize API
        $this->api = new AIBPG_API();

        // Initialize keyword research
        $this->keyword_research = new AIBPG_Keyword_Research();

        // Initialize content generator
        $this->content_generator = new AIBPG_Content_Generator();

        // Initialize formatter
        $this->formatter = new AIBPG_Formatter();

        // Initialize internal links manager
        $this->internal_links = new AIBPG_Internal_Links();

        // Initialize sitemap analyzer
        $this->sitemap_analyzer = new AIBPG_Sitemap_Analyzer();

        // Add actions and filters
        add_action('plugins_loaded', array($this, 'load_textdomain'));
    }

    /**
     * Load plugin textdomain
     */
    public function load_textdomain() {
        load_plugin_textdomain('ai-blog-post-generator', false, dirname(plugin_basename(__FILE__)) . '/languages');
    }

    /**
     * Plugin activation
     */
    public function activate() {
        // Create necessary database tables
        $this->create_tables();

        // Set default options
        $this->set_default_options();

        // Flush rewrite rules
        flush_rewrite_rules();
    }

    /**
     * Create necessary database tables
     */
    private function create_tables() {
        global $wpdb;

        $charset_collate = $wpdb->get_charset_collate();

        // Table for storing generated posts
        $table_name = $wpdb->prefix . 'aibpg_posts';

        $sql = "CREATE TABLE $table_name (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            post_id bigint(20) NOT NULL,
            keyword varchar(255) NOT NULL,
            ai_service varchar(50) NOT NULL,
            created_at datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY  (id),
            KEY post_id (post_id)
        ) $charset_collate;";

        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
    }

    /**
     * Set default options
     */
    private function set_default_options() {
        // Default AI service
        if (!get_option('aibpg_default_ai_service')) {
            update_option('aibpg_default_ai_service', 'openai');
        }

        // Default post length
        if (!get_option('aibpg_post_length')) {
            update_option('aibpg_post_length', '2000');
        }

        // Default internal links count
        if (!get_option('aibpg_internal_links_count')) {
            update_option('aibpg_internal_links_count', '6');
        }

        // Initialize counters
        if (!get_option('aibpg_posts_generated')) {
            update_option('aibpg_posts_generated', 0);
        }

        if (!get_option('aibpg_posts_saved')) {
            update_option('aibpg_posts_saved', 0);
        }

        if (!get_option('aibpg_validated_api_keys')) {
            update_option('aibpg_validated_api_keys', 0);
        }

        // Set default Gemini model
        if (!get_option('aibpg_gemini_preferred_model')) {
            update_option('aibpg_gemini_preferred_model', 'gemini-pro');
        }

        // Set default Gemini models
        if (!get_option('aibpg_gemini_available_models')) {
            $default_models = array(
                'gemini-pro' => 'Gemini Pro',
                'gemini-pro-vision' => 'Gemini Pro Vision',
                'gemini-1.5-pro' => 'Gemini 1.5 Pro',
                'gemini-1.5-flash' => 'Gemini 1.5 Flash'
            );
            update_option('aibpg_gemini_available_models', $default_models);
        }
    }

    /**
     * Plugin deactivation
     */
    public function deactivate() {
        // Clean up if needed

        // Flush rewrite rules
        flush_rewrite_rules();
    }
}

// Initialize the plugin
function aibpg_init() {
    return AI_Blog_Post_Generator::get_instance();
}

// Start the plugin
$GLOBALS['ai_blog_post_generator'] = aibpg_init();
