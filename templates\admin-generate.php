<div class="wrap">
    <h1><?php echo esc_html__('Generate AI Blog Post', 'ai-blog-post-generator'); ?></h1>

    <div class="aibpg-generate-container">
        <div class="aibpg-generate-form">
            <form id="aibpg-generate-form" method="post">
                <?php wp_nonce_field('aibpg_generate_post', 'aibpg_nonce'); ?>

                <div class="aibpg-form-section">
                    <h2><?php echo esc_html__('Step 1: Choose Keyword Strategy', 'ai-blog-post-generator'); ?></h2>

                    <div class="aibpg-tabs">
                        <div class="aibpg-tab-nav">
                            <button type="button" class="aibpg-tab-button active" data-tab="auto-research"><?php echo esc_html__('Auto Research', 'ai-blog-post-generator'); ?></button>
                            <button type="button" class="aibpg-tab-button" data-tab="manual-keyword"><?php echo esc_html__('Manual Keyword', 'ai-blog-post-generator'); ?></button>
                        </div>

                        <div class="aibpg-tab-content active" id="auto-research-content">
                            <p><?php echo esc_html__('Let AI analyze your website and find the best keyword to target based on your site authority, competition, and search volume.', 'ai-blog-post-generator'); ?></p>

                            <div class="aibpg-form-field">
                                <label for="aibpg-niche"><?php echo esc_html__('Website Niche', 'ai-blog-post-generator'); ?></label>
                                <select name="aibpg_niche" id="aibpg-niche">
                                    <option value="health-fitness"><?php echo esc_html__('Health & Fitness', 'ai-blog-post-generator'); ?></option>
                                    <option value="nutrition"><?php echo esc_html__('Nutrition', 'ai-blog-post-generator'); ?></option>
                                    <option value="weight-loss"><?php echo esc_html__('Weight Loss', 'ai-blog-post-generator'); ?></option>
                                    <option value="workout"><?php echo esc_html__('Workout & Exercise', 'ai-blog-post-generator'); ?></option>
                                    <option value="yoga"><?php echo esc_html__('Yoga & Meditation', 'ai-blog-post-generator'); ?></option>
                                    <option value="supplements"><?php echo esc_html__('Supplements', 'ai-blog-post-generator'); ?></option>
                                    <option value="equipment"><?php echo esc_html__('Fitness Equipment', 'ai-blog-post-generator'); ?></option>
                                </select>
                            </div>

                            <button type="button" id="aibpg-research-button" class="button button-primary"><?php echo esc_html__('Research Keywords', 'ai-blog-post-generator'); ?></button>

                            <div id="aibpg-research-results" class="aibpg-research-results" style="display: none;">
                                <h3><?php echo esc_html__('Recommended Keywords', 'ai-blog-post-generator'); ?></h3>
                                <div class="aibpg-keywords-list"></div>
                            </div>
                        </div>

                        <div class="aibpg-tab-content" id="manual-keyword-content" style="display: none;">
                            <p><?php echo esc_html__('Enter a specific keyword you want to target.', 'ai-blog-post-generator'); ?></p>

                            <div class="aibpg-form-field">
                                <label for="aibpg-keyword"><?php echo esc_html__('Target Keyword', 'ai-blog-post-generator'); ?></label>
                                <input type="text" name="aibpg_keyword" id="aibpg-keyword" class="regular-text" placeholder="<?php echo esc_attr__('e.g., best protein powder for weight loss', 'ai-blog-post-generator'); ?>">
                            </div>
                        </div>
                    </div>
                </div>

                <div class="aibpg-form-section">
                    <h2><?php echo esc_html__('Step 2: Configure Post Settings', 'ai-blog-post-generator'); ?></h2>

                    <div class="aibpg-form-field">
                        <label for="aibpg-category"><?php echo esc_html__('Category', 'ai-blog-post-generator'); ?></label>
                        <select name="aibpg_category" id="aibpg-category">
                            <?php foreach ($categories as $category) : ?>
                                <option value="<?php echo esc_attr($category->term_id); ?>"><?php echo esc_html($category->name); ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>

                    <div class="aibpg-form-field">
                        <label for="aibpg-post-status"><?php echo esc_html__('Post Status', 'ai-blog-post-generator'); ?></label>
                        <select name="aibpg_post_status" id="aibpg-post-status">
                            <option value="draft"><?php echo esc_html__('Draft', 'ai-blog-post-generator'); ?></option>
                            <option value="publish"><?php echo esc_html__('Publish', 'ai-blog-post-generator'); ?></option>
                        </select>
                    </div>

                    <div class="aibpg-form-field">
                        <label for="aibpg-ai-service"><?php echo esc_html__('AI Service', 'ai-blog-post-generator'); ?></label>
                        <select name="aibpg_ai_service" id="aibpg-ai-service">
                            <option value=""><?php echo esc_html__('Auto (use default)', 'ai-blog-post-generator'); ?></option>
                            <?php
                            $default_service = get_option('aibpg_default_ai_service', '');
                            $services = array(
                                'openai' => __('OpenAI', 'ai-blog-post-generator'),
                                'claude' => __('Claude', 'ai-blog-post-generator'),
                                'gemini' => __('Gemini', 'ai-blog-post-generator'),
                                'openrouter' => __('OpenRouter', 'ai-blog-post-generator')
                            );

                            // Hidden status elements for JavaScript to check
                            foreach ($services as $value => $label) {
                                $api_key = get_option('aibpg_' . $value . '_api_key', '');
                                $is_configured = !empty($api_key) ? 'true' : 'false';
                                echo '<div id="aibpg-api-key-status-' . esc_attr($value) . '" data-configured="' . $is_configured . '" style="display:none;"></div>';
                            }

                            foreach ($services as $value => $label) {
                                $api_key = get_option('aibpg_' . $value . '_api_key', '');

                                if (!empty($api_key)) {
                                    $selected = selected($default_service, $value, false);
                                    echo '<option value="' . esc_attr($value) . '" ' . $selected . '>' . esc_html($label) . '</option>';
                                }
                            }
                            ?>
                        </select>
                        <p class="description"><?php echo esc_html__('Select which AI service to use for content generation.', 'ai-blog-post-generator'); ?></p>
                    </div>
                </div>

                <div class="aibpg-form-section">
                    <h2><?php echo esc_html__('Step 3: Generate Post', 'ai-blog-post-generator'); ?></h2>

                    <div id="aibpg-api-key-warning" class="aibpg-warning-message" style="display: none;">
                        <p><?php echo esc_html__('API key not configured or validated for the selected AI service.', 'ai-blog-post-generator'); ?></p>
                        <a href="<?php echo esc_url(admin_url('admin.php?page=ai-blog-post-generator-settings')); ?>" class="button button-secondary"><?php echo esc_html__('Go to Settings', 'ai-blog-post-generator'); ?></a>
                    </div>

                    <div class="aibpg-form-field">
                        <button type="button" id="aibpg-generate-button" class="button button-primary button-large"><?php echo esc_html__('Generate Blog Post', 'ai-blog-post-generator'); ?></button>
                    </div>
                </div>
            </form>
        </div>

        <div class="aibpg-generate-preview" style="display: none;">
            <h2><?php echo esc_html__('Generated Post Preview', 'ai-blog-post-generator'); ?></h2>

            <div class="aibpg-preview-content">
                <div class="aibpg-preview-title"></div>
                <div class="aibpg-preview-body"></div>
            </div>

            <div class="aibpg-preview-actions">
                <button type="button" id="aibpg-save-post" class="button button-primary"><?php echo esc_html__('Save Post', 'ai-blog-post-generator'); ?></button>
                <button type="button" id="aibpg-regenerate" class="button button-secondary"><?php echo esc_html__('Regenerate', 'ai-blog-post-generator'); ?></button>
            </div>
        </div>
    </div>
</div>
