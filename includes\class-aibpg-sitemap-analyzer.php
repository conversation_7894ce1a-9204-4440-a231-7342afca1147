<?php
/**
 * Sitemap Analyzer class for AI Blog Post Generator
 *
 * This class provides advanced sitemap analysis capabilities to improve internal linking
 * and content relevance.
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

class AIBPG_Sitemap_Analyzer {
    /**
     * Constructor
     */
    public function __construct() {
        // Register AJAX handlers
        add_action('wp_ajax_aibpg_analyze_sitemaps', array($this, 'ajax_analyze_sitemaps'));
    }

    /**
     * AJAX handler for sitemap analysis
     */
    public function ajax_analyze_sitemaps() {
        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'aibpg_nonce')) {
            wp_send_json_error(array('message' => __('Security check failed.', 'ai-blog-post-generator')));
        }

        // Get sitemap URLs
        $sitemap_urls = isset($_POST['sitemap_urls']) ? sanitize_textarea_field($_POST['sitemap_urls']) : '';

        if (empty($sitemap_urls)) {
            wp_send_json_error(array('message' => __('No sitemap URLs provided.', 'ai-blog-post-generator')));
        }

        // Split URLs by newline
        $urls = preg_split('/\r\n|\r|\n/', $sitemap_urls);
        $urls = array_map('trim', $urls);
        $urls = array_filter($urls);

        if (empty($urls)) {
            wp_send_json_error(array('message' => __('No valid sitemap URLs found.', 'ai-blog-post-generator')));
        }

        // Analyze sitemaps
        $results = $this->analyze_sitemaps($urls);

        if ($results['success']) {
            wp_send_json_success(array(
                'message' => sprintf(
                    __('Successfully analyzed %d sitemaps with %d URLs. Found %d potential internal linking opportunities.', 'ai-blog-post-generator'),
                    $results['sitemaps_analyzed'],
                    $results['urls_found'],
                    $results['linking_opportunities']
                ),
                'data' => $results
            ));
        } else {
            wp_send_json_error(array('message' => $results['message']));
        }
    }

    /**
     * Analyze multiple sitemaps
     *
     * @param array $urls Sitemap URLs to analyze
     * @return array Analysis results
     */
    public function analyze_sitemaps($urls) {
        $results = array(
            'success' => true,
            'sitemaps_analyzed' => 0,
            'urls_found' => 0,
            'linking_opportunities' => 0,
            'categories' => array(),
            'top_urls' => array(),
            'message' => ''
        );

        $all_urls = array();
        $errors = array();

        foreach ($urls as $url) {
            $sitemap_result = $this->analyze_single_sitemap($url);

            if ($sitemap_result['success']) {
                $results['sitemaps_analyzed']++;
                $results['urls_found'] += count($sitemap_result['urls']);
                $all_urls = array_merge($all_urls, $sitemap_result['urls']);

                // Merge categories
                foreach ($sitemap_result['categories'] as $category => $count) {
                    if (!isset($results['categories'][$category])) {
                        $results['categories'][$category] = 0;
                    }
                    $results['categories'][$category] += $count;
                }
            } else {
                $errors[] = sprintf(__('Error analyzing sitemap %s: %s', 'ai-blog-post-generator'), $url, $sitemap_result['message']);
            }
        }

        // Sort URLs by priority and last modified date
        usort($all_urls, function($a, $b) {
            // First compare by priority (higher priority first)
            if (isset($a['priority']) && isset($b['priority'])) {
                if ($a['priority'] != $b['priority']) {
                    return $b['priority'] <=> $a['priority'];
                }
            }

            // Then compare by last modified date (newer first)
            if (isset($a['lastmod']) && isset($b['lastmod'])) {
                return strtotime($b['lastmod']) <=> strtotime($a['lastmod']);
            }

            return 0;
        });

        // Get top URLs
        $results['top_urls'] = array_slice($all_urls, 0, 50);

        // Calculate linking opportunities
        $results['linking_opportunities'] = $this->calculate_linking_opportunities($all_urls);

        // Store results in transient
        set_transient('aibpg_sitemap_analysis', $results, DAY_IN_SECONDS);

        // Add errors to results if any
        if (!empty($errors)) {
            $results['errors'] = $errors;
            $results['message'] = implode('. ', $errors);
        }

        return $results;
    }

    /**
     * Analyze a single sitemap
     *
     * @param string $url Sitemap URL
     * @return array Analysis results
     */
    private function analyze_single_sitemap($url) {
        $result = array(
            'success' => false,
            'urls' => array(),
            'categories' => array(),
            'message' => ''
        );

        // Get sitemap content
        $response = wp_remote_get($url, array('timeout' => 30));

        if (is_wp_error($response)) {
            $result['message'] = $response->get_error_message();
            return $result;
        }

        $body = wp_remote_retrieve_body($response);

        if (empty($body)) {
            $result['message'] = __('Empty sitemap content.', 'ai-blog-post-generator');
            return $result;
        }

        // Check if it's a sitemap index
        if (strpos($body, '<sitemapindex') !== false) {
            return $this->process_sitemap_index($body, $url);
        }

        // Process regular sitemap
        return $this->process_sitemap($body, $url);
    }

    /**
     * Process a sitemap index
     *
     * @param string $content Sitemap index content
     * @param string $base_url Base URL for relative URLs
     * @return array Processing results
     */
    private function process_sitemap_index($content, $base_url) {
        $result = array(
            'success' => true,
            'urls' => array(),
            'categories' => array(),
            'message' => ''
        );

        // Load XML
        libxml_use_internal_errors(true);
        $xml = simplexml_load_string($content);

        if ($xml === false) {
            $result['success'] = false;
            $result['message'] = __('Invalid XML in sitemap index.', 'ai-blog-post-generator');
            return $result;
        }

        // Get all sitemap URLs
        $sitemap_urls = array();
        foreach ($xml->sitemap as $sitemap) {
            $loc = (string) $sitemap->loc;
            $sitemap_urls[] = $loc;
        }

        // Process each sitemap
        foreach ($sitemap_urls as $sitemap_url) {
            $sitemap_result = $this->analyze_single_sitemap($sitemap_url);

            if ($sitemap_result['success']) {
                $result['urls'] = array_merge($result['urls'], $sitemap_result['urls']);

                // Merge categories
                foreach ($sitemap_result['categories'] as $category => $count) {
                    if (!isset($result['categories'][$category])) {
                        $result['categories'][$category] = 0;
                    }
                    $result['categories'][$category] += $count;
                }
            }
        }

        return $result;
    }

    /**
     * Process a regular sitemap
     *
     * @param string $content Sitemap content
     * @param string $base_url Base URL for relative URLs
     * @return array Processing results
     */
    private function process_sitemap($content, $base_url) {
        $result = array(
            'success' => true,
            'urls' => array(),
            'categories' => array(),
            'message' => ''
        );

        // Load XML
        libxml_use_internal_errors(true);
        $xml = simplexml_load_string($content);

        if ($xml === false) {
            $result['success'] = false;
            $result['message'] = __('Invalid XML in sitemap.', 'ai-blog-post-generator');
            return $result;
        }

        // Get all URLs
        foreach ($xml->url as $url) {
            $loc = (string) $url->loc;
            $lastmod = isset($url->lastmod) ? (string) $url->lastmod : '';
            $priority = isset($url->priority) ? (float) $url->priority : 0.5;

            // Extract categories from URL
            $categories = $this->extract_categories_from_url($loc);

            // Add URL to results
            $result['urls'][] = array(
                'loc' => $loc,
                'lastmod' => $lastmod,
                'priority' => $priority,
                'categories' => $categories
            );

            // Update category counts
            foreach ($categories as $category) {
                if (!isset($result['categories'][$category])) {
                    $result['categories'][$category] = 0;
                }
                $result['categories'][$category]++;
            }
        }

        return $result;
    }

    /**
     * Extract categories from URL
     *
     * @param string $url URL to analyze
     * @return array Categories found in URL
     */
    private function extract_categories_from_url($url) {
        $categories = array();

        // Parse URL
        $parsed_url = parse_url($url);

        if (!isset($parsed_url['path'])) {
            return $categories;
        }

        $path = $parsed_url['path'];

        // Remove trailing slash
        $path = rtrim($path, '/');

        // Split path into segments
        $segments = explode('/', $path);

        // Remove empty segments
        $segments = array_filter($segments);

        // Common category indicators in URLs
        $category_indicators = array('category', 'cat', 'topics', 'section', 'tag');

        // Check if any segment is a category indicator
        foreach ($segments as $i => $segment) {
            if (in_array($segment, $category_indicators) && isset($segments[$i + 1])) {
                $categories[] = $segments[$i + 1];
            }
        }

        // If no categories found, use the last segment as a potential category
        if (empty($categories) && !empty($segments)) {
            $last_segment = end($segments);

            // Only use if it looks like a category (not a post slug with numbers or dates)
            if (!preg_match('/^\d{4}\/\d{2}\/\d{2}/', $last_segment) && !preg_match('/\d+$/', $last_segment)) {
                $categories[] = $last_segment;
            }
        }

        return $categories;
    }

    /**
     * Calculate linking opportunities
     *
     * @param array $urls URLs from sitemaps
     * @return int Number of linking opportunities
     */
    private function calculate_linking_opportunities($urls) {
        // Group URLs by category
        $category_groups = array();

        foreach ($urls as $url_data) {
            if (empty($url_data['categories'])) {
                continue;
            }

            foreach ($url_data['categories'] as $category) {
                if (!isset($category_groups[$category])) {
                    $category_groups[$category] = array();
                }

                $category_groups[$category][] = $url_data['loc'];
            }
        }

        // Count potential links between pages in the same category
        $opportunities = 0;

        foreach ($category_groups as $category => $urls) {
            $count = count($urls);

            if ($count > 1) {
                // Each URL can link to all others in the same category
                $opportunities += $count * ($count - 1);
            }
        }

        return $opportunities;
    }
}