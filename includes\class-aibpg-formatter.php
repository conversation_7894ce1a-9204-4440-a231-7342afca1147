<?php
/**
 * Formatter class for AI Blog Post Generator
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

class AIBPG_Formatter {
    /**
     * Constructor
     */
    public function __construct() {
        // Nothing to do here for now
    }

    /**
     * Format content for WordPress
     *
     * @param string $content The content to format
     * @return string Formatted content
     */
    public function format_content($content) {
        // Convert markdown to HTML
        $content = $this->markdown_to_html($content);
        
        // Add internal links
        $content = $this->add_internal_links($content);
        
        // Add schema markup
        $content = $this->add_schema_markup($content);
        
        return $content;
    }

    /**
     * Convert markdown to HTML
     *
     * @param string $markdown The markdown content
     * @return string HTML content
     */
    private function markdown_to_html($markdown) {
        // Check if Parsedown is available
        if (!class_exists('Parsedown')) {
            // Include Parsedown library
            require_once AIBPG_PLUGIN_DIR . 'includes/lib/Parsedown.php';
        }
        
        // If Parsedown is still not available, use simple regex-based conversion
        if (!class_exists('Parsedown')) {
            return $this->simple_markdown_to_html($markdown);
        }
        
        // Use Parsedown
        $parsedown = new Parsedown();
        $html = $parsedown->text($markdown);
        
        return $html;
    }

    /**
     * Simple markdown to HTML conversion using regex
     *
     * @param string $markdown The markdown content
     * @return string HTML content
     */
    private function simple_markdown_to_html($markdown) {
        // Headers
        $markdown = preg_replace('/^# (.*?)$/m', '<h1>$1</h1>', $markdown);
        $markdown = preg_replace('/^## (.*?)$/m', '<h2>$1</h2>', $markdown);
        $markdown = preg_replace('/^### (.*?)$/m', '<h3>$1</h3>', $markdown);
        $markdown = preg_replace('/^#### (.*?)$/m', '<h4>$1</h4>', $markdown);
        $markdown = preg_replace('/^##### (.*?)$/m', '<h5>$1</h5>', $markdown);
        $markdown = preg_replace('/^###### (.*?)$/m', '<h6>$1</h6>', $markdown);
        
        // Bold
        $markdown = preg_replace('/\*\*(.*?)\*\*/s', '<strong>$1</strong>', $markdown);
        $markdown = preg_replace('/__(.*?)__/s', '<strong>$1</strong>', $markdown);
        
        // Italic
        $markdown = preg_replace('/\*(.*?)\*/s', '<em>$1</em>', $markdown);
        $markdown = preg_replace('/_(.*?)_/s', '<em>$1</em>', $markdown);
        
        // Lists
        $markdown = preg_replace('/^- (.*?)$/m', '<li>$1</li>', $markdown);
        $markdown = preg_replace('/^[0-9]+\. (.*?)$/m', '<li>$1</li>', $markdown);
        $markdown = preg_replace('/<\/li>\n<li>/s', '</li><li>', $markdown);
        $markdown = preg_replace('/<li>(.*?)<\/li>/s', '<ul><li>$1</li></ul>', $markdown);
        $markdown = preg_replace('/<\/ul>\n<ul>/s', '', $markdown);
        
        // Links
        $markdown = preg_replace('/\[(.*?)\]\((.*?)\)/s', '<a href="$2">$1</a>', $markdown);
        
        // Paragraphs
        $markdown = preg_replace('/^([^<].*?)$/m', '<p>$1</p>', $markdown);
        $markdown = preg_replace('/<\/p>\n<p>/s', '</p><p>', $markdown);
        
        // Clean up empty paragraphs
        $markdown = preg_replace('/<p><\/p>/s', '', $markdown);
        
        return $markdown;
    }

    /**
     * Add internal links to content
     *
     * @param string $content The content
     * @return string Content with internal links
     */
    public function add_internal_links($content) {
        // Get internal links count
        $links_count = get_option('aibpg_internal_links_count', 6);
        
        // Check if content already has enough links
        $existing_links_count = substr_count($content, '<a href');
        
        if ($existing_links_count >= $links_count) {
            return $content;
        }
        
        // Get more links to add
        $links_to_add = $links_count - $existing_links_count;
        $internal_links = $this->get_internal_links($links_to_add);
        
        if (empty($internal_links)) {
            return $content;
        }
        
        // Add links to content
        foreach ($internal_links as $url => $title) {
            // Find a suitable place to add the link
            $anchor_text = $this->get_anchor_text($content, $title);
            
            if (empty($anchor_text)) {
                continue;
            }
            
            // Replace first occurrence only
            $pattern = '/\b' . preg_quote($anchor_text, '/') . '\b(?![^<]*>|[^<>]*<\/a>)/i';
            $replacement = '<a href="' . esc_url($url) . '">' . $anchor_text . '</a>';
            $content = preg_replace($pattern, $replacement, $content, 1);
        }
        
        return $content;
    }

    /**
     * Get internal links for inclusion in the blog post
     *
     * @param int $count Number of links to get
     * @return array Internal links (URL => Title)
     */
    private function get_internal_links($count) {
        $links = array();
        
        // Get published posts
        $posts = get_posts(array(
            'post_type' => 'post',
            'post_status' => 'publish',
            'numberposts' => $count,
            'orderby' => 'rand'
        ));
        
        if (empty($posts)) {
            // If no posts found, return empty array
            return $links;
        }
        
        foreach ($posts as $post) {
            $links[get_permalink($post->ID)] = $post->post_title;
            
            if (count($links) >= $count) {
                break;
            }
        }
        
        return $links;
    }

    /**
     * Get suitable anchor text for a link
     *
     * @param string $content The content
     * @param string $title The post title
     * @return string Anchor text
     */
    private function get_anchor_text($content, $title) {
        // Try to find a suitable anchor text based on the title
        $words = explode(' ', $title);
        
        // Try with full title
        if (stripos($content, $title) !== false) {
            return $title;
        }
        
        // Try with 3-word combinations
        if (count($words) >= 3) {
            for ($i = 0; $i <= count($words) - 3; $i++) {
                $phrase = $words[$i] . ' ' . $words[$i + 1] . ' ' . $words[$i + 2];
                
                if (stripos($content, $phrase) !== false) {
                    return $phrase;
                }
            }
        }
        
        // Try with 2-word combinations
        if (count($words) >= 2) {
            for ($i = 0; $i <= count($words) - 2; $i++) {
                $phrase = $words[$i] . ' ' . $words[$i + 1];
                
                if (stripos($content, $phrase) !== false) {
                    return $phrase;
                }
            }
        }
        
        // Try with single words (longer than 4 characters)
        foreach ($words as $word) {
            if (strlen($word) > 4 && stripos($content, $word) !== false) {
                return $word;
            }
        }
        
        return '';
    }

    /**
     * Add schema markup to content
     *
     * @param string $content The content
     * @return string Content with schema markup
     */
    public function add_schema_markup($content) {
        // Extract headings
        preg_match_all('/<h([1-6])>(.*?)<\/h[1-6]>/i', $content, $headings);
        
        if (empty($headings[0])) {
            return $content;
        }
        
        // Build table of contents
        $toc = '<div class="aibpg-toc"><h4>' . __('Table of Contents', 'ai-blog-post-generator') . '</h4><ul>';
        
        foreach ($headings[0] as $index => $heading) {
            $level = $headings[1][$index];
            $text = $headings[2][$index];
            $id = 'heading-' . sanitize_title($text);
            
            // Add ID to heading
            $content = str_replace($heading, '<h' . $level . ' id="' . $id . '">' . $text . '</h' . $level . '>', $content);
            
            // Add to TOC
            $toc .= '<li class="toc-level-' . $level . '"><a href="#' . $id . '">' . $text . '</a></li>';
        }
        
        $toc .= '</ul></div>';
        
        // Add TOC after first heading
        $content = preg_replace('/(<h[1-6]>.*?<\/h[1-6]>)/i', '$1' . $toc, $content, 1);
        
        return $content;
    }
}
