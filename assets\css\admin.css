/* Admin styles for AI Blog Post Generator - Modern UI 2023 Edition */

/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700&display=swap');

/* Modern Design System with Enhanced UI */
:root {
    /* Primary brand colors - Modern Gradient Palette */
    --aibpg-primary: #4f46e5;
    --aibpg-primary-dark: #3730a3;
    --aibpg-primary-light: #e0e7ff;
    --aibpg-primary-50: #eef2ff;
    --aibpg-primary-100: #e0e7ff;
    --aibpg-primary-200: #c7d2fe;
    --aibpg-primary-300: #a5b4fc;
    --aibpg-primary-400: #818cf8;
    --aibpg-primary-500: #6366f1;
    --aibpg-primary-600: #4f46e5;
    --aibpg-primary-700: #4338ca;
    --aibpg-primary-800: #3730a3;
    --aibpg-primary-900: #312e81;

    /* Secondary colors */
    --aibpg-secondary: #64748b;
    --aibpg-secondary-50: #f8fafc;
    --aibpg-secondary-100: #f1f5f9;
    --aibpg-secondary-200: #e2e8f0;
    --aibpg-secondary-300: #cbd5e1;
    --aibpg-secondary-400: #94a3b8;
    --aibpg-secondary-500: #64748b;
    --aibpg-secondary-600: #475569;
    --aibpg-secondary-700: #334155;
    --aibpg-secondary-800: #1e293b;
    --aibpg-secondary-900: #0f172a;

    /* Semantic colors */
    --aibpg-success: #10b981;
    --aibpg-success-light: #d1fae5;
    --aibpg-danger: #ef4444;
    --aibpg-danger-light: #fee2e2;
    --aibpg-warning: #f59e0b;
    --aibpg-warning-light: #fef3c7;
    --aibpg-info: #3b82f6;
    --aibpg-info-light: #dbeafe;

    /* Neutrals */
    --aibpg-white: #ffffff;
    --aibpg-black: #000000;
    --aibpg-gray-50: #f9fafb;
    --aibpg-gray-100: #f3f4f6;
    --aibpg-gray-200: #e5e7eb;
    --aibpg-gray-300: #d1d5db;
    --aibpg-gray-400: #9ca3af;
    --aibpg-gray-500: #6b7280;
    --aibpg-gray-600: #4b5563;
    --aibpg-gray-700: #374151;
    --aibpg-gray-800: #1f2937;
    --aibpg-gray-900: #111827;

    /* Design tokens */
    --aibpg-border-radius-sm: 0.25rem;
    --aibpg-border-radius: 0.5rem;
    --aibpg-border-radius-md: 0.75rem;
    --aibpg-border-radius-lg: 1rem;
    --aibpg-border-radius-xl: 1.5rem;
    --aibpg-border-radius-2xl: 2rem;
    --aibpg-border-radius-full: 9999px;

    /* Shadows */
    --aibpg-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --aibpg-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --aibpg-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --aibpg-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --aibpg-shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --aibpg-shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    --aibpg-shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);

    /* Transitions */
    --aibpg-transition-fast: all 150ms cubic-bezier(0.4, 0, 0.2, 1);
    --aibpg-transition: all 300ms cubic-bezier(0.4, 0, 0.2, 1);
    --aibpg-transition-slow: all 500ms cubic-bezier(0.4, 0, 0.2, 1);

    /* Typography - Modern Font System */
    --aibpg-font-family: 'Inter', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
    --aibpg-font-family-heading: 'Poppins', 'Inter', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
    --aibpg-font-size-xs: 0.75rem;
    --aibpg-font-size-sm: 0.875rem;
    --aibpg-font-size-base: 1rem;
    --aibpg-font-size-lg: 1.125rem;
    --aibpg-font-size-xl: 1.25rem;
    --aibpg-font-size-2xl: 1.5rem;
    --aibpg-font-size-3xl: 1.875rem;
    --aibpg-font-size-4xl: 2.25rem;
    --aibpg-font-size-5xl: 3rem;
    --aibpg-font-weight-light: 300;
    --aibpg-font-weight-normal: 400;
    --aibpg-font-weight-medium: 500;
    --aibpg-font-weight-semibold: 600;
    --aibpg-font-weight-bold: 700;
    --aibpg-line-height-none: 1;
    --aibpg-line-height-tight: 1.25;
    --aibpg-line-height-snug: 1.375;
    --aibpg-line-height-normal: 1.5;
    --aibpg-line-height-relaxed: 1.625;
    --aibpg-line-height-loose: 2;
    --aibpg-letter-spacing-tight: -0.025em;
    --aibpg-letter-spacing-normal: 0;
    --aibpg-letter-spacing-wide: 0.025em;
    --aibpg-letter-spacing-wider: 0.05em;

    /* Spacing */
    --aibpg-spacing-1: 0.25rem;
    --aibpg-spacing-2: 0.5rem;
    --aibpg-spacing-3: 0.75rem;
    --aibpg-spacing-4: 1rem;
    --aibpg-spacing-5: 1.25rem;
    --aibpg-spacing-6: 1.5rem;
    --aibpg-spacing-8: 2rem;
    --aibpg-spacing-10: 2.5rem;
    --aibpg-spacing-12: 3rem;
    --aibpg-spacing-16: 4rem;
    --aibpg-spacing-20: 5rem;
    --aibpg-spacing-24: 6rem;
    --aibpg-spacing-32: 8rem;
}

/* General Styles */
.aibpg-settings,
.aibpg-dashboard {
    margin-top: var(--aibpg-spacing-8);
    font-family: var(--aibpg-font-family);
    color: var(--aibpg-gray-800);
}

/* Modern Form Styles */
.aibpg-form-field {
    margin-bottom: var(--aibpg-spacing-6);
    position: relative;
}

.aibpg-form-field label {
    display: block;
    margin-bottom: var(--aibpg-spacing-2);
    font-weight: var(--aibpg-font-weight-medium);
    color: var(--aibpg-gray-700);
    font-size: var(--aibpg-font-size-sm);
}

.aibpg-form-field input[type="text"],
.aibpg-form-field input[type="password"],
.aibpg-form-field input[type="email"],
.aibpg-form-field input[type="number"],
.aibpg-form-field select,
.aibpg-form-field textarea {
    width: 100%;
    padding: var(--aibpg-spacing-3) var(--aibpg-spacing-4);
    border: 2px solid var(--aibpg-gray-200);
    border-radius: var(--aibpg-border-radius-md);
    background-color: var(--aibpg-white);
    color: var(--aibpg-gray-800);
    font-size: var(--aibpg-font-size-base);
    line-height: var(--aibpg-line-height-normal);
    transition: var(--aibpg-transition);
    box-shadow: var(--aibpg-shadow-sm);
}

.aibpg-form-field input[type="text"]:focus,
.aibpg-form-field input[type="password"]:focus,
.aibpg-form-field input[type="email"]:focus,
.aibpg-form-field input[type="number"]:focus,
.aibpg-form-field select:focus,
.aibpg-form-field textarea:focus {
    border-color: var(--aibpg-primary-400);
    box-shadow: 0 0 0 3px var(--aibpg-primary-100);
    outline: none;
}

.aibpg-form-field .description {
    font-size: var(--aibpg-font-size-xs);
    color: var(--aibpg-gray-500);
    margin-top: var(--aibpg-spacing-2);
    display: block;
}

/* Input states */
.aibpg-input-error {
    border-color: var(--aibpg-danger-500) !important;
    animation: shake 0.5s cubic-bezier(.36,.07,.19,.97) both;
    transform: translate3d(0, 0, 0);
    backface-visibility: hidden;
    perspective: 1000px;
}

.aibpg-input-success {
    border-color: var(--aibpg-success-500) !important;
    background-color: var(--aibpg-success-50) !important;
    transition: all 0.3s ease;
}

@keyframes shake {
    10%, 90% { transform: translate3d(-1px, 0, 0); }
    20%, 80% { transform: translate3d(2px, 0, 0); }
    30%, 50%, 70% { transform: translate3d(-4px, 0, 0); }
    40%, 60% { transform: translate3d(4px, 0, 0); }
}

/* Alert styles */
.aibpg-alert {
    padding: var(--aibpg-spacing-4);
    border-radius: var(--aibpg-border-radius-md);
    margin-bottom: var(--aibpg-spacing-4);
    display: flex;
    align-items: flex-start;
    font-size: var(--aibpg-font-size-sm);
    line-height: 1.5;
    border-left: 4px solid transparent;
}

.aibpg-alert .dashicons {
    margin-right: var(--aibpg-spacing-3);
    font-size: 18px;
    width: 18px;
    height: 18px;
}

.aibpg-alert-success {
    background-color: var(--aibpg-success-50);
    color: var(--aibpg-success-700);
    border-left-color: var(--aibpg-success-500);
}

.aibpg-alert-danger {
    background-color: var(--aibpg-danger-50);
    color: var(--aibpg-danger-700);
    border-left-color: var(--aibpg-danger-500);
}

.aibpg-alert-warning {
    background-color: var(--aibpg-warning-50);
    color: var(--aibpg-warning-700);
    border-left-color: var(--aibpg-warning-500);
}

.aibpg-alert-info {
    background-color: var(--aibpg-info-50);
    color: var(--aibpg-info-700);
    border-left-color: var(--aibpg-info-500);
}

/* Reset some WordPress styles */
.wrap h1 {
    font-family: var(--aibpg-font-family);
    font-weight: 600;
    font-size: var(--aibpg-font-size-3xl);
    color: var(--aibpg-gray-900);
    margin-bottom: var(--aibpg-spacing-6);
    line-height: 1.2;
}

/* Dashboard Header */
.aibpg-dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--aibpg-spacing-10);
    background: linear-gradient(135deg, var(--aibpg-primary-100) 0%, var(--aibpg-white) 100%);
    padding: var(--aibpg-spacing-10);
    border-radius: var(--aibpg-border-radius-lg);
    box-shadow: var(--aibpg-shadow-lg);
    position: relative;
    overflow: hidden;
    transition: var(--aibpg-transition);
    border: 1px solid var(--aibpg-primary-200);
}

.aibpg-dashboard-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('data:image/svg+xml;utf8,<svg width="100" height="100" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg"><path d="M0 0 L50 0 L0 50 Z" fill="%234a6cf7" opacity="0.1"/><path d="M100 100 L100 50 L50 100 Z" fill="%234a6cf7" opacity="0.1"/></svg>');
    background-size: 100px 100px;
    opacity: 0.5;
    z-index: 0;
}

.aibpg-dashboard-welcome {
    flex: 2;
    position: relative;
    z-index: 1;
}

.aibpg-dashboard-welcome h2 {
    font-size: var(--aibpg-font-size-4xl);
    margin-top: 0;
    margin-bottom: var(--aibpg-spacing-4);
    color: var(--aibpg-primary-700);
    font-weight: 700;
    line-height: 1.2;
    letter-spacing: -0.025em;
    background: linear-gradient(135deg, var(--aibpg-primary-600), var(--aibpg-primary-800));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-fill-color: transparent;
}

.aibpg-dashboard-welcome p {
    font-size: var(--aibpg-font-size-lg);
    margin-bottom: var(--aibpg-spacing-6);
    max-width: 600px;
    color: var(--aibpg-gray-600);
    line-height: 1.6;
}

.aibpg-dashboard-stats {
    flex: 1;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    gap: var(--aibpg-spacing-5);
    position: relative;
    z-index: 1;
}

.aibpg-stat-box {
    text-align: center;
    padding: var(--aibpg-spacing-6);
    background: var(--aibpg-white);
    border-radius: var(--aibpg-border-radius-lg);
    min-width: 140px;
    box-shadow: var(--aibpg-shadow-md);
    transition: var(--aibpg-transition);
    position: relative;
    overflow: hidden;
    border: 1px solid var(--aibpg-gray-100);
}

.aibpg-stat-box::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(90deg, var(--aibpg-primary-400), var(--aibpg-primary-600));
}

.aibpg-stat-box:hover {
    transform: translateY(-5px);
    box-shadow: var(--aibpg-shadow-lg);
}

.aibpg-stat-box h3 {
    font-size: var(--aibpg-font-size-4xl);
    margin: 0 0 var(--aibpg-spacing-2);
    color: var(--aibpg-primary-600);
    font-weight: 700;
    line-height: 1.2;
}

.aibpg-stat-box p {
    margin: 0;
    color: var(--aibpg-gray-500);
    font-size: var(--aibpg-font-size-sm);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.aibpg-dashboard-content {
    background: var(--aibpg-white);
    padding: var(--aibpg-spacing-10);
    border-radius: var(--aibpg-border-radius-lg);
    box-shadow: var(--aibpg-shadow-md);
    margin-bottom: var(--aibpg-spacing-10);
    border: 1px solid var(--aibpg-gray-100);
}

.aibpg-actions {
    display: flex;
    flex-wrap: wrap;
    gap: var(--aibpg-spacing-8);
    margin-top: var(--aibpg-spacing-8);
}

.aibpg-action-box {
    flex: 1;
    min-width: 280px;
    padding: var(--aibpg-spacing-8);
    background: var(--aibpg-white);
    border-radius: var(--aibpg-border-radius-lg);
    box-shadow: var(--aibpg-shadow-md);
    transition: var(--aibpg-transition);
    position: relative;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    border: 1px solid var(--aibpg-gray-100);
}

.aibpg-action-box::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(90deg, var(--aibpg-primary-400), var(--aibpg-primary-600));
}

.aibpg-action-box:hover {
    transform: translateY(-5px);
    box-shadow: var(--aibpg-shadow-lg);
}

.aibpg-action-box .dashicons {
    font-size: 36px;
    width: 36px;
    height: 36px;
    color: var(--aibpg-primary-500);
    margin-bottom: var(--aibpg-spacing-4);
    transition: var(--aibpg-transition);
    background: var(--aibpg-primary-100);
    border-radius: var(--aibpg-border-radius-full);
    padding: var(--aibpg-spacing-3);
    display: flex;
    align-items: center;
    justify-content: center;
}

.aibpg-action-box:hover .dashicons {
    transform: scale(1.1) rotate(5deg);
    background: var(--aibpg-primary-200);
}

.aibpg-action-box h3 {
    margin-top: 0;
    margin-bottom: var(--aibpg-spacing-3);
    color: var(--aibpg-gray-900);
    font-size: var(--aibpg-font-size-xl);
    font-weight: 600;
}

.aibpg-action-box p {
    color: var(--aibpg-gray-600);
    margin-bottom: var(--aibpg-spacing-6);
    line-height: 1.6;
    flex-grow: 1;
}

/* Modern Button Styles */
.aibpg-button,
.aibpg-action-box .button,
.aibpg-form-field .button,
.aibpg-validate-key {
    background: linear-gradient(135deg, var(--aibpg-primary-500), var(--aibpg-primary-600)) !important;
    border-color: var(--aibpg-primary-600) !important;
    color: white !important;
    padding: var(--aibpg-spacing-3) var(--aibpg-spacing-5) !important;
    height: auto !important;
    line-height: 1.5 !important;
    font-size: var(--aibpg-font-size-sm) !important;
    font-weight: 500 !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    border-radius: var(--aibpg-border-radius-md) !important;
    text-shadow: none !important;
    box-shadow: var(--aibpg-shadow-sm) !important;
    align-self: flex-start !important;
    display: inline-flex !important;
    align-items: center !important;
    position: relative !important;
    overflow: hidden !important;
    border: none !important;
    cursor: pointer !important;
}

.aibpg-button::before,
.aibpg-action-box .button::before,
.aibpg-form-field .button::before,
.aibpg-validate-key::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0));
    transform: translateY(-100%);
    transition: transform 0.3s ease;
}

.aibpg-button:hover,
.aibpg-action-box .button:hover,
.aibpg-form-field .button:hover,
.aibpg-validate-key:hover {
    background: linear-gradient(135deg, var(--aibpg-primary-600), var(--aibpg-primary-700)) !important;
    border-color: var(--aibpg-primary-700) !important;
    color: white !important;
    transform: translateY(-2px) !important;
    box-shadow: var(--aibpg-shadow-md) !important;
}

.aibpg-button:hover::before,
.aibpg-action-box .button:hover::before,
.aibpg-form-field .button:hover::before,
.aibpg-validate-key:hover::before {
    transform: translateY(0);
}

.aibpg-button:focus,
.aibpg-action-box .button:focus,
.aibpg-form-field .button:focus,
.aibpg-validate-key:focus {
    box-shadow: 0 0 0 2px white, 0 0 0 4px var(--aibpg-primary-500) !important;
    outline: none !important;
}

.aibpg-button:active,
.aibpg-action-box .button:active,
.aibpg-form-field .button:active,
.aibpg-validate-key:active {
    transform: translateY(0) !important;
    box-shadow: var(--aibpg-shadow-sm) !important;
}

.aibpg-button.button-secondary,
.aibpg-action-box .button.button-secondary,
.aibpg-form-field .button.button-secondary {
    background: var(--aibpg-white) !important;
    border: 1px solid var(--aibpg-gray-300) !important;
    color: var(--aibpg-gray-700) !important;
}

.aibpg-button.button-secondary:hover,
.aibpg-action-box .button.button-secondary:hover,
.aibpg-form-field .button.button-secondary:hover {
    background: var(--aibpg-gray-50) !important;
    border-color: var(--aibpg-gray-400) !important;
    color: var(--aibpg-gray-900) !important;
}

.aibpg-button:disabled,
.aibpg-action-box .button:disabled,
.aibpg-form-field .button:disabled,
.aibpg-validate-key:disabled {
    background: var(--aibpg-gray-300) !important;
    border-color: var(--aibpg-gray-300) !important;
    color: var(--aibpg-gray-500) !important;
    cursor: not-allowed !important;
    transform: none !important;
    box-shadow: none !important;
    opacity: 0.7;
}

/* Button highlight animation for drawing attention */
.aibpg-button-highlight {
    animation: button-pulse 1.5s cubic-bezier(0.4, 0, 0.6, 1) infinite !important;
    box-shadow: 0 0 0 0 rgba(99, 102, 241, 0.7) !important;
    position: relative;
    overflow: hidden;
}

.aibpg-button-highlight::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255,255,255,0.3) 0%, rgba(255,255,255,0) 70%);
    opacity: 0;
    animation: button-ripple 1.5s cubic-bezier(0.4, 0, 0.6, 1) infinite;
    pointer-events: none;
}

@keyframes button-pulse {
    0% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(99, 102, 241, 0.7);
    }
    70% {
        transform: scale(1.05);
        box-shadow: 0 0 0 10px rgba(99, 102, 241, 0);
    }
    100% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(99, 102, 241, 0);
    }
}

@keyframes button-ripple {
    0% {
        opacity: 0;
        transform: scale(0.5);
    }
    50% {
        opacity: 0.5;
    }
    100% {
        opacity: 0;
        transform: scale(1);
    }
}

/* Button with icon */
.aibpg-button .dashicons,
.aibpg-action-box .button .dashicons,
.aibpg-form-field .button .dashicons {
    margin-right: var(--aibpg-spacing-2);
    font-size: 16px;
    width: 16px;
    height: 16px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.aibpg-cta-button {
    display: inline-flex;
    align-items: center;
    padding: var(--aibpg-spacing-4) var(--aibpg-spacing-8) !important;
    font-size: var(--aibpg-font-size-lg) !important;
    font-weight: 600 !important;
    margin-top: var(--aibpg-spacing-6);
    box-shadow: var(--aibpg-shadow-md) !important;
    transition: var(--aibpg-transition) !important;
    border-radius: var(--aibpg-border-radius-md) !important;
    background: linear-gradient(135deg, var(--aibpg-primary-500), var(--aibpg-primary-700)) !important;
    border: none !important;
    position: relative;
    overflow: hidden;
}

.aibpg-cta-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0));
    transform: translateY(-100%);
    transition: var(--aibpg-transition);
}

.aibpg-cta-button:hover {
    transform: translateY(-3px);
    box-shadow: var(--aibpg-shadow-lg) !important;
    background: linear-gradient(135deg, var(--aibpg-primary-600), var(--aibpg-primary-800)) !important;
}

.aibpg-cta-button:hover::before {
    transform: translateY(0);
}

.aibpg-cta-button .dashicons {
    margin-right: var(--aibpg-spacing-3);
    font-size: 18px;
    height: 18px;
    width: 18px;
}

/* Features grid */
.aibpg-features {
    margin-top: var(--aibpg-spacing-12);
    padding: var(--aibpg-spacing-10);
    background: var(--aibpg-white);
    border-radius: var(--aibpg-border-radius-lg);
    box-shadow: var(--aibpg-shadow-md);
    border: 1px solid var(--aibpg-gray-100);
}

.aibpg-features h3 {
    margin-top: 0;
    margin-bottom: var(--aibpg-spacing-8);
    color: var(--aibpg-gray-900);
    font-size: var(--aibpg-font-size-2xl);
    font-weight: 700;
    text-align: center;
    position: relative;
    padding-bottom: var(--aibpg-spacing-4);
}

.aibpg-features h3::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 4px;
    background: linear-gradient(90deg, var(--aibpg-primary-400), var(--aibpg-primary-600));
    border-radius: var(--aibpg-border-radius-full);
}

.aibpg-features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: var(--aibpg-spacing-6);
    margin-top: var(--aibpg-spacing-8);
}

.aibpg-feature-item {
    padding: var(--aibpg-spacing-6);
    background: var(--aibpg-primary-50);
    border-radius: var(--aibpg-border-radius-lg);
    transition: var(--aibpg-transition);
    text-align: center;
    position: relative;
    overflow: hidden;
    border: 1px solid var(--aibpg-primary-100);
}

.aibpg-feature-item:hover {
    transform: translateY(-5px);
    box-shadow: var(--aibpg-shadow-md);
    background: var(--aibpg-primary-100);
}

.aibpg-feature-item .dashicons {
    font-size: 32px;
    width: 64px;
    height: 64px;
    color: var(--aibpg-primary-600);
    margin-bottom: var(--aibpg-spacing-4);
    transition: var(--aibpg-transition);
    background: white;
    border-radius: var(--aibpg-border-radius-full);
    padding: var(--aibpg-spacing-4);
    display: inline-flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--aibpg-shadow-sm);
}

.aibpg-feature-item:hover .dashicons {
    transform: scale(1.1) rotate(5deg);
    color: var(--aibpg-primary-700);
}

.aibpg-feature-item h4 {
    margin-top: 0;
    margin-bottom: var(--aibpg-spacing-2);
    color: var(--aibpg-gray-900);
    font-size: var(--aibpg-font-size-lg);
    font-weight: 600;
}

.aibpg-feature-item p {
    color: var(--aibpg-gray-600);
    margin-bottom: 0;
    line-height: 1.6;
    font-size: var(--aibpg-font-size-sm);
}

/* Steps */
.aibpg-help {
    margin-top: var(--aibpg-spacing-12);
    padding: var(--aibpg-spacing-10);
    background: var(--aibpg-white);
    border-radius: var(--aibpg-border-radius-lg);
    box-shadow: var(--aibpg-shadow-md);
    border: 1px solid var(--aibpg-gray-100);
}

.aibpg-help h3 {
    margin-top: 0;
    margin-bottom: var(--aibpg-spacing-8);
    color: var(--aibpg-gray-900);
    font-size: var(--aibpg-font-size-2xl);
    font-weight: 700;
    text-align: center;
    position: relative;
    padding-bottom: var(--aibpg-spacing-4);
}

.aibpg-help h3::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 4px;
    background: linear-gradient(90deg, var(--aibpg-primary-400), var(--aibpg-primary-600));
    border-radius: var(--aibpg-border-radius-full);
}

.aibpg-steps {
    margin-top: var(--aibpg-spacing-8);
    position: relative;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--aibpg-spacing-6);
}

.aibpg-step {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    position: relative;
    z-index: 1;
    background: var(--aibpg-gray-50);
    border-radius: var(--aibpg-border-radius-lg);
    padding: var(--aibpg-spacing-6);
    box-shadow: var(--aibpg-shadow-sm);
    transition: var(--aibpg-transition);
    border: 1px solid var(--aibpg-gray-100);
}

.aibpg-step:hover {
    transform: translateY(-5px);
    box-shadow: var(--aibpg-shadow-md);
    background: white;
}

.aibpg-step-number {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--aibpg-primary-500), var(--aibpg-primary-700));
    color: white;
    border-radius: var(--aibpg-border-radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    font-size: var(--aibpg-font-size-xl);
    margin-bottom: var(--aibpg-spacing-4);
    flex-shrink: 0;
    box-shadow: var(--aibpg-shadow-md);
    position: relative;
}

.aibpg-step-number::after {
    content: '';
    position: absolute;
    top: -5px;
    left: -5px;
    right: -5px;
    bottom: -5px;
    border-radius: var(--aibpg-border-radius-full);
    border: 2px dashed var(--aibpg-primary-300);
    opacity: 0.5;
}

.aibpg-step-content {
    flex-grow: 1;
    position: relative;
}

.aibpg-step-content h4 {
    margin-top: 0;
    margin-bottom: var(--aibpg-spacing-3);
    color: var(--aibpg-gray-900);
    font-size: var(--aibpg-font-size-lg);
    font-weight: 600;
}

.aibpg-step-content p {
    margin: 0;
    color: var(--aibpg-gray-600);
    line-height: 1.6;
    font-size: var(--aibpg-font-size-sm);
}

.aibpg-notice {
    padding: var(--aibpg-spacing-6);
    background: var(--aibpg-primary-50);
    border: 1px solid var(--aibpg-primary-200);
    border-left: 4px solid var(--aibpg-primary-500);
    margin-bottom: var(--aibpg-spacing-6);
    border-radius: var(--aibpg-border-radius-md);
    position: relative;
    box-shadow: var(--aibpg-shadow-sm);
}

.aibpg-notice p {
    margin: 0 0 var(--aibpg-spacing-3);
    color: var(--aibpg-gray-700);
    line-height: 1.6;
    font-size: var(--aibpg-font-size-base);
}

.aibpg-notice p:last-child {
    margin-bottom: 0;
}

.aibpg-notice.aibpg-notice-success {
    background-color: var(--aibpg-success-light);
    border-color: var(--aibpg-success);
    border-left-color: var(--aibpg-success);
}

.aibpg-notice.aibpg-notice-warning {
    background-color: var(--aibpg-warning-light);
    border-color: var(--aibpg-warning);
    border-left-color: var(--aibpg-warning);
}

.aibpg-notice.aibpg-notice-error {
    background-color: var(--aibpg-danger-light);
    border-color: var(--aibpg-danger);
    border-left-color: var(--aibpg-danger);
}

.aibpg-notice.aibpg-notice-info {
    background-color: var(--aibpg-info-light);
    border-color: var(--aibpg-info);
    border-left-color: var(--aibpg-info);
}

/* Tabs */
.nav-tab-wrapper {
    margin-bottom: var(--aibpg-spacing-8);
    border-bottom: 1px solid var(--aibpg-gray-200);
    position: relative;
    display: flex;
    overflow-x: auto;
    padding-bottom: 0;
    gap: var(--aibpg-spacing-6);
}

.nav-tab {
    background: transparent;
    border: none;
    border-bottom: 3px solid transparent;
    margin-left: 0;
    margin-right: 0;
    padding: var(--aibpg-spacing-4) var(--aibpg-spacing-2);
    font-size: var(--aibpg-font-size-base);
    font-weight: 600;
    color: var(--aibpg-gray-600);
    transition: var(--aibpg-transition);
    position: relative;
}

.nav-tab:hover {
    color: var(--aibpg-primary-600);
    background: transparent;
}

.nav-tab-active,
.nav-tab-active:focus,
.nav-tab-active:focus:active,
.nav-tab-active:hover {
    border-bottom-color: var(--aibpg-primary-500);
    background: transparent;
    color: var(--aibpg-primary-600);
}

.nav-tab::after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 0;
    width: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--aibpg-primary-400), var(--aibpg-primary-600));
    transition: var(--aibpg-transition);
}

.nav-tab:hover::after {
    width: 100%;
}

.nav-tab-active::after {
    width: 100%;
}

.aibpg-tab-content {
    display: none;
    animation: fadeIn 0.5s ease;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.aibpg-tab-content.active {
    display: block;
}

/* Form fields */
.aibpg-form-section {
    margin-bottom: var(--aibpg-spacing-8);
    padding: var(--aibpg-spacing-8);
    background: var(--aibpg-white);
    border-radius: var(--aibpg-border-radius-lg);
    box-shadow: var(--aibpg-shadow-md);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    border: 1px solid var(--aibpg-gray-100);
}

.aibpg-form-section:hover {
    transform: translateY(-5px);
    box-shadow: var(--aibpg-shadow-lg);
}

.aibpg-form-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(90deg, var(--aibpg-primary-400), var(--aibpg-primary-600));
}

.aibpg-form-section h2 {
    margin-top: 0;
    margin-bottom: var(--aibpg-spacing-6);
    color: var(--aibpg-gray-900);
    font-size: var(--aibpg-font-size-2xl);
    font-weight: var(--aibpg-font-weight-bold);
    padding-bottom: var(--aibpg-spacing-4);
    border-bottom: 1px solid var(--aibpg-gray-200);
    font-family: var(--aibpg-font-family-heading);
    letter-spacing: var(--aibpg-letter-spacing-tight);
    position: relative;
}

.aibpg-form-section h2::after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 0;
    width: 80px;
    height: 3px;
    background: linear-gradient(90deg, var(--aibpg-primary-500), var(--aibpg-primary-400));
    border-radius: var(--aibpg-border-radius-full);
}

.aibpg-form-field {
    margin-bottom: var(--aibpg-spacing-6);
}

.aibpg-form-field label {
    display: block;
    margin-bottom: var(--aibpg-spacing-2);
    font-weight: 600;
    color: var(--aibpg-gray-700);
    font-size: var(--aibpg-font-size-sm);
}

.aibpg-form-field input[type="text"],
.aibpg-form-field input[type="number"],
.aibpg-form-field input[type="password"],
.aibpg-form-field input[type="email"],
.aibpg-form-field select,
.aibpg-form-field textarea {
    width: 100%;
    padding: var(--aibpg-spacing-3) var(--aibpg-spacing-4);
    border: 1px solid var(--aibpg-gray-300);
    border-radius: var(--aibpg-border-radius-md);
    background-color: var(--aibpg-white);
    color: var(--aibpg-gray-700);
    font-size: var(--aibpg-font-size-base);
    line-height: 1.5;
    transition: var(--aibpg-transition);
    box-shadow: var(--aibpg-shadow-sm);
}

.aibpg-form-field input[type="text"]:hover,
.aibpg-form-field input[type="number"]:hover,
.aibpg-form-field input[type="password"]:hover,
.aibpg-form-field input[type="email"]:hover,
.aibpg-form-field select:hover,
.aibpg-form-field textarea:hover {
    border-color: var(--aibpg-gray-400);
}

.aibpg-form-field input[type="text"]:focus,
.aibpg-form-field input[type="number"]:focus,
.aibpg-form-field input[type="password"]:focus,
.aibpg-form-field input[type="email"]:focus,
.aibpg-form-field select:focus,
.aibpg-form-field textarea:focus {
    border-color: var(--aibpg-primary-500);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.2);
    outline: none;
}

.aibpg-form-field .description {
    margin-top: var(--aibpg-spacing-2);
    font-size: var(--aibpg-font-size-xs);
    color: var(--aibpg-gray-500);
    line-height: 1.5;
}

/* Custom select styling */
.aibpg-form-field select {
    appearance: none;
    background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%236b7280' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M6 9l6 6 6-6'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 10px center;
    background-size: 16px;
    padding-right: 40px;
}

.aibpg-validation-result {
    margin-top: var(--aibpg-spacing-4);
    padding: var(--aibpg-spacing-4);
    display: none;
    border-radius: var(--aibpg-border-radius-md);
    font-size: var(--aibpg-font-size-sm);
    line-height: 1.5;
    position: relative;
    animation: slideIn 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
    border: 1px solid transparent;
    box-shadow: var(--aibpg-shadow-md);
    overflow: hidden;
}

.aibpg-validation-result::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
}

@keyframes slideIn {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
}

.aibpg-validation-success {
    background-color: var(--aibpg-success-50);
    color: var(--aibpg-success-700);
    border-color: rgba(16, 185, 129, 0.2);
}

.aibpg-validation-success::before {
    background: linear-gradient(to bottom, var(--aibpg-success-500), var(--aibpg-success-600));
}

.aibpg-validation-error {
    background-color: var(--aibpg-danger-50);
    color: var(--aibpg-danger-700);
    border-color: rgba(239, 68, 68, 0.2);
}

.aibpg-validation-error::before {
    background: linear-gradient(to bottom, var(--aibpg-danger-500), var(--aibpg-danger-600));
}

/* Countdown timer for rate limiting */
.aibpg-countdown {
    margin-top: var(--aibpg-spacing-3);
    font-size: var(--aibpg-font-size-xs);
    color: var(--aibpg-gray-600);
    background: var(--aibpg-gray-100);
    padding: var(--aibpg-spacing-2) var(--aibpg-spacing-3);
    border-radius: var(--aibpg-border-radius-md);
    display: inline-block;
}

.aibpg-countdown-timer {
    font-weight: var(--aibpg-font-weight-bold);
    color: var(--aibpg-danger-600);
}

.aibpg-validation-result::before {
    font-family: dashicons;
    font-size: 20px;
    line-height: 1;
    margin-right: var(--aibpg-spacing-3);
    vertical-align: middle;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
}

.aibpg-validation-success::before {
    content: "\f147"; /* Dashicon: yes */
    color: var(--aibpg-success);
    background: white;
    border-radius: var(--aibpg-border-radius-full);
    box-shadow: var(--aibpg-shadow-sm);
}

.aibpg-validation-error::before {
    content: "\f335"; /* Dashicon: no */
    color: var(--aibpg-danger);
    background: white;
    border-radius: var(--aibpg-border-radius-full);
    box-shadow: var(--aibpg-shadow-sm);
}

.aibpg-warning-message {
    background-color: var(--aibpg-warning-light);
    color: var(--aibpg-warning);
    border: 1px solid var(--aibpg-warning);
    border-left: 4px solid var(--aibpg-warning);
    padding: var(--aibpg-spacing-6);
    margin-bottom: var(--aibpg-spacing-6);
    border-radius: var(--aibpg-border-radius-md);
    position: relative;
    box-shadow: var(--aibpg-shadow-sm);
}

.aibpg-warning-message::before {
    content: "\f534"; /* Dashicon: warning */
    font-family: dashicons;
    font-size: 24px;
    line-height: 1;
    margin-right: var(--aibpg-spacing-4);
    vertical-align: middle;
    color: var(--aibpg-warning);
    background: white;
    border-radius: var(--aibpg-border-radius-full);
    padding: var(--aibpg-spacing-2);
    display: inline-flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--aibpg-shadow-sm);
    width: 28px;
    height: 28px;
}

.aibpg-warning-message p {
    margin: 0 0 var(--aibpg-spacing-4) 0;
    line-height: 1.6;
    font-size: var(--aibpg-font-size-base);
    display: inline-block;
}

.aibpg-warning-message .button {
    margin-right: var(--aibpg-spacing-4);
    background: var(--aibpg-warning);
    border-color: var(--aibpg-warning);
    color: white;
    font-weight: 500;
    padding: var(--aibpg-spacing-2) var(--aibpg-spacing-4);
    height: auto;
    line-height: 1.5;
    transition: var(--aibpg-transition);
    border-radius: var(--aibpg-border-radius-md);
    box-shadow: var(--aibpg-shadow-sm);
}

.aibpg-warning-message .button:hover {
    background: var(--aibpg-warning);
    filter: brightness(0.9);
    transform: translateY(-2px);
    box-shadow: var(--aibpg-shadow-md);
}

/* Generate page */
.aibpg-generate-container {
    display: flex;
    flex-wrap: wrap;
    gap: var(--aibpg-spacing-8);
    margin-top: var(--aibpg-spacing-8);
}

.aibpg-generate-form {
    flex: 1;
    min-width: 350px;
}

.aibpg-generate-preview {
    flex: 1.5;
    min-width: 350px;
    background: var(--aibpg-white);
    padding: var(--aibpg-spacing-8);
    border-radius: var(--aibpg-border-radius-lg);
    box-shadow: var(--aibpg-shadow-lg);
    position: relative;
    transition: var(--aibpg-transition);
    border: 1px solid var(--aibpg-gray-100);
    overflow: hidden;
}

.aibpg-generate-preview::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(90deg, var(--aibpg-primary-400), var(--aibpg-primary-600));
}

.aibpg-preview-title {
    font-size: var(--aibpg-font-size-3xl);
    font-weight: 700;
    margin-bottom: var(--aibpg-spacing-6);
    padding-bottom: var(--aibpg-spacing-4);
    border-bottom: 1px solid var(--aibpg-gray-200);
    color: var(--aibpg-gray-900);
    line-height: 1.3;
}

.aibpg-preview-body {
    max-height: 650px;
    overflow-y: auto;
    padding-right: var(--aibpg-spacing-4);
    line-height: 1.7;
    color: var(--aibpg-gray-700);
    font-size: var(--aibpg-font-size-base);
    scrollbar-width: thin;
    scrollbar-color: var(--aibpg-gray-300) var(--aibpg-gray-100);
}

.aibpg-preview-body::-webkit-scrollbar {
    width: 8px;
}

.aibpg-preview-body::-webkit-scrollbar-track {
    background: var(--aibpg-gray-100);
    border-radius: var(--aibpg-border-radius-full);
}

.aibpg-preview-body::-webkit-scrollbar-thumb {
    background-color: var(--aibpg-gray-300);
    border-radius: var(--aibpg-border-radius-full);
    border: 2px solid var(--aibpg-gray-100);
}

.aibpg-preview-body h1,
.aibpg-preview-body h2,
.aibpg-preview-body h3,
.aibpg-preview-body h4,
.aibpg-preview-body h5,
.aibpg-preview-body h6 {
    margin-top: 1.5em;
    margin-bottom: 0.75em;
    color: var(--aibpg-gray-800);
    font-weight: 600;
    line-height: 1.3;
}

.aibpg-preview-body h2 {
    font-size: 24px;
    border-bottom: 1px solid var(--aibpg-gray-200);
    padding-bottom: 10px;
}

.aibpg-preview-body h3 {
    font-size: 20px;
}

.aibpg-preview-body p {
    margin-bottom: 1.5em;
}

.aibpg-preview-body ul,
.aibpg-preview-body ol {
    margin-bottom: 1.5em;
    padding-left: 2em;
}

.aibpg-preview-body li {
    margin-bottom: 0.5em;
}

.aibpg-preview-body blockquote {
    border-left: 4px solid var(--aibpg-primary);
    padding: 0.5em 1em;
    margin: 0 0 1.5em 0;
    background: var(--aibpg-primary-light);
    border-radius: 0 var(--aibpg-border-radius) var(--aibpg-border-radius) 0;
}

.aibpg-preview-body pre {
    background: var(--aibpg-gray-100);
    padding: 15px;
    border-radius: var(--aibpg-border-radius);
    overflow-x: auto;
    margin-bottom: 1.5em;
}

.aibpg-preview-body code {
    background: var(--aibpg-gray-100);
    padding: 2px 5px;
    border-radius: 3px;
    font-size: 0.9em;
}

.aibpg-preview-actions {
    margin-top: var(--aibpg-spacing-8);
    padding-top: var(--aibpg-spacing-6);
    border-top: 1px solid var(--aibpg-gray-200);
    display: flex;
    gap: var(--aibpg-spacing-4);
    flex-wrap: wrap;
}

.aibpg-preview-actions .button {
    padding: var(--aibpg-spacing-3) var(--aibpg-spacing-6);
    height: auto;
    line-height: 1.5;
    font-size: var(--aibpg-font-size-sm);
    font-weight: 500;
    transition: var(--aibpg-transition);
    border-radius: var(--aibpg-border-radius-md);
    box-shadow: var(--aibpg-shadow-sm);
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.aibpg-preview-actions .button::before {
    font-family: dashicons;
    font-size: 16px;
    margin-right: var(--aibpg-spacing-2);
    line-height: 1;
}

.aibpg-preview-actions .button-primary {
    background: linear-gradient(135deg, var(--aibpg-primary-500), var(--aibpg-primary-600));
    border-color: var(--aibpg-primary-600);
    color: white;
}

.aibpg-preview-actions .button-primary::before {
    content: "\f147"; /* Dashicon: yes */
}

.aibpg-preview-actions .button-primary:hover {
    background: linear-gradient(135deg, var(--aibpg-primary-600), var(--aibpg-primary-700));
    border-color: var(--aibpg-primary-700);
    transform: translateY(-2px);
    box-shadow: var(--aibpg-shadow-md);
}

.aibpg-preview-actions .button-secondary {
    color: var(--aibpg-gray-700);
    border-color: var(--aibpg-gray-300);
    background: white;
}

.aibpg-preview-actions .button-secondary::before {
    content: "\f463"; /* Dashicon: update */
}

.aibpg-preview-actions .button-secondary:hover {
    background: var(--aibpg-gray-50);
    border-color: var(--aibpg-gray-400);
    transform: translateY(-2px);
    box-shadow: var(--aibpg-shadow-md);
}

/* Tabs in generate page */
.aibpg-tabs {
    margin-bottom: var(--aibpg-spacing-8);
}

.aibpg-tab-nav {
    display: flex;
    border-bottom: 1px solid var(--aibpg-gray-200);
    margin-bottom: var(--aibpg-spacing-6);
    position: relative;
    gap: var(--aibpg-spacing-6);
}

.aibpg-tab-button {
    padding: var(--aibpg-spacing-4) var(--aibpg-spacing-2);
    background: transparent;
    border: none;
    border-bottom: 3px solid transparent;
    cursor: pointer;
    font-size: var(--aibpg-font-size-base);
    font-weight: 600;
    color: var(--aibpg-gray-600);
    transition: var(--aibpg-transition);
    position: relative;
    outline: none;
}

.aibpg-tab-button:hover {
    color: var(--aibpg-primary-600);
}

.aibpg-tab-button.active {
    color: var(--aibpg-primary-600);
}

.aibpg-tab-button::after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 0;
    width: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--aibpg-primary-400), var(--aibpg-primary-600));
    transition: var(--aibpg-transition);
    border-radius: var(--aibpg-border-radius-full) var(--aibpg-border-radius-full) 0 0;
}

.aibpg-tab-button:hover::after {
    width: 100%;
}

.aibpg-tab-button.active::after {
    width: 100%;
}

/* Research results */
.aibpg-research-results {
    margin-top: var(--aibpg-spacing-6);
    padding: var(--aibpg-spacing-6);
    background: var(--aibpg-white);
    border-radius: var(--aibpg-border-radius-lg);
    box-shadow: var(--aibpg-shadow-md);
    position: relative;
    overflow: hidden;
    border: 1px solid var(--aibpg-gray-100);
}

.aibpg-research-results::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(90deg, var(--aibpg-primary-400), var(--aibpg-primary-600));
}

.aibpg-research-results h3 {
    margin-top: 0;
    margin-bottom: var(--aibpg-spacing-5);
    color: var(--aibpg-gray-900);
    font-size: var(--aibpg-font-size-xl);
    font-weight: 600;
    display: flex;
    align-items: center;
}

.aibpg-research-results h3::before {
    content: "\f179"; /* Dashicon: search */
    font-family: dashicons;
    margin-right: var(--aibpg-spacing-3);
    color: var(--aibpg-primary-500);
    background: var(--aibpg-primary-50);
    width: 32px;
    height: 32px;
    border-radius: var(--aibpg-border-radius-full);
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.aibpg-keywords-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: var(--aibpg-spacing-5);
    margin-top: var(--aibpg-spacing-5);
}

.aibpg-keyword-item {
    padding: var(--aibpg-spacing-5);
    background: var(--aibpg-white);
    border: 1px solid var(--aibpg-gray-200);
    border-radius: var(--aibpg-border-radius-md);
    cursor: pointer;
    transition: var(--aibpg-transition);
    position: relative;
    overflow: hidden;
    box-shadow: var(--aibpg-shadow-sm);
}

.aibpg-keyword-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(to bottom, var(--aibpg-primary-400), var(--aibpg-primary-600));
    opacity: 0;
    transition: var(--aibpg-transition);
}

.aibpg-keyword-item:hover {
    border-color: var(--aibpg-primary-300);
    box-shadow: var(--aibpg-shadow-md);
    transform: translateY(-3px);
}

.aibpg-keyword-item:hover::before {
    opacity: 1;
}

.aibpg-keyword-item.selected {
    border-color: var(--aibpg-primary-400);
    background-color: var(--aibpg-primary-50);
    box-shadow: var(--aibpg-shadow-lg);
    transform: translateY(-3px);
    transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.aibpg-keyword-item.selected::before {
    opacity: 1;
}

.aibpg-keyword-item.selected::after {
    content: '\f147'; /* Dashicon checkmark */
    font-family: dashicons;
    position: absolute;
    top: 10px;
    right: 10px;
    color: var(--aibpg-primary-600);
    font-size: 20px;
    background: white;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--aibpg-shadow-md);
    animation: scaleIn 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
}

@keyframes scaleIn {
    from { transform: scale(0); opacity: 0; }
    to { transform: scale(1); opacity: 1; }
}

.aibpg-keyword-title {
    font-weight: 600;
    margin-bottom: var(--aibpg-spacing-3);
    color: var(--aibpg-gray-900);
    font-size: var(--aibpg-font-size-base);
    line-height: 1.4;
}

.aibpg-keyword-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: var(--aibpg-font-size-xs);
    color: var(--aibpg-gray-600);
    margin-top: var(--aibpg-spacing-4);
    padding-top: var(--aibpg-spacing-4);
    border-top: 1px solid var(--aibpg-gray-200);
}

.aibpg-keyword-volume {
    display: inline-flex;
    align-items: center;
    background: var(--aibpg-primary-50);
    color: var(--aibpg-primary-600);
    padding: var(--aibpg-spacing-1) var(--aibpg-spacing-2);
    border-radius: var(--aibpg-border-radius-full);
    font-weight: 500;
    font-size: var(--aibpg-font-size-xs);
    border: 1px solid var(--aibpg-primary-200);
    box-shadow: var(--aibpg-shadow-sm);
}

.aibpg-keyword-volume::before {
    content: "\f185"; /* Dashicon: chart */
    font-family: dashicons;
    font-size: 14px;
    margin-right: var(--aibpg-spacing-1);
}

.aibpg-keyword-difficulty {
    display: inline-flex;
    align-items: center;
    padding: var(--aibpg-spacing-1) var(--aibpg-spacing-2);
    border-radius: var(--aibpg-border-radius-full);
    font-weight: 500;
    font-size: var(--aibpg-font-size-xs);
    box-shadow: var(--aibpg-shadow-sm);
}

.aibpg-difficulty-indicator {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: var(--aibpg-spacing-1);
}

.aibpg-difficulty-easy {
    background: var(--aibpg-success-light);
    color: var(--aibpg-success);
    border: 1px solid rgba(16, 185, 129, 0.2);
}

.aibpg-difficulty-easy .aibpg-difficulty-indicator {
    background-color: var(--aibpg-success);
}

.aibpg-difficulty-medium {
    background: var(--aibpg-warning-light);
    color: var(--aibpg-warning);
    border: 1px solid rgba(245, 158, 11, 0.2);
}

.aibpg-difficulty-medium .aibpg-difficulty-indicator {
    background-color: var(--aibpg-warning);
}

.aibpg-difficulty-hard {
    background: var(--aibpg-danger-light);
    color: var(--aibpg-danger);
    border: 1px solid rgba(239, 68, 68, 0.2);
}

.aibpg-difficulty-hard .aibpg-difficulty-indicator {
    background-color: var(--aibpg-danger);
}

/* Loading indicators */
.aibpg-loading {
    display: inline-block;
    position: relative;
    width: 24px;
    height: 24px;
    margin-left: var(--aibpg-spacing-3);
    vertical-align: middle;
}

.aibpg-loading:after {
    content: " ";
    display: block;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    border: 2px solid var(--aibpg-primary-500);
    border-color: var(--aibpg-primary-500) transparent var(--aibpg-primary-500) transparent;
    animation: aibpg-loading 1.2s linear infinite;
    box-shadow: 0 0 10px rgba(99, 102, 241, 0.2);
}

.aibpg-loading-container {
    margin: var(--aibpg-spacing-8) 0;
    padding: var(--aibpg-spacing-8);
    background: var(--aibpg-white);
    border-radius: var(--aibpg-border-radius-lg);
    box-shadow: var(--aibpg-shadow-lg);
    text-align: center;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
    position: relative;
    overflow: hidden;
    border: 1px solid var(--aibpg-gray-100);
}

.aibpg-loading-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(90deg, var(--aibpg-primary-400), var(--aibpg-primary-600));
}

.aibpg-step-icon {
    margin-bottom: var(--aibpg-spacing-6);
    animation: aibpg-pulse 1.5s ease-in-out infinite;
    background: var(--aibpg-primary-100);
    width: 100px;
    height: 100px;
    border-radius: var(--aibpg-border-radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: auto;
    margin-right: auto;
    position: relative;
    border: 2px solid var(--aibpg-primary-300);
    box-shadow: var(--aibpg-shadow-lg), 0 0 20px rgba(99, 102, 241, 0.3);
}

.aibpg-step-icon::after {
    content: '';
    position: absolute;
    top: -8px;
    left: -8px;
    right: -8px;
    bottom: -8px;
    border-radius: var(--aibpg-border-radius-full);
    border: 2px dashed var(--aibpg-primary-300);
    opacity: 0.5;
    animation: aibpg-pulse 2s ease-in-out infinite;
}

.aibpg-step-icon .dashicons {
    font-size: 48px;
    width: 48px;
    height: 48px;
    color: var(--aibpg-primary-600);
    transition: var(--aibpg-transition);
    filter: drop-shadow(0 0 8px rgba(99, 102, 241, 0.3));
}

.aibpg-step-icon:hover .dashicons {
    transform: scale(1.1) rotate(5deg);
}

.aibpg-status-message {
    margin-bottom: var(--aibpg-spacing-5);
    font-weight: 600;
    color: var(--aibpg-gray-800);
    font-size: var(--aibpg-font-size-lg);
    transition: var(--aibpg-transition);
    line-height: 1.4;
}

.aibpg-progress-bar {
    height: 16px;
    background-color: var(--aibpg-gray-100);
    border-radius: var(--aibpg-border-radius-full);
    overflow: hidden;
    margin: var(--aibpg-spacing-4) 0;
    position: relative;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid var(--aibpg-gray-200);
}

.aibpg-progress-bar-inner {
    height: 100%;
    background: linear-gradient(90deg, var(--aibpg-primary-400), var(--aibpg-primary-600));
    border-radius: var(--aibpg-border-radius-full);
    width: 0;
    transition: width 0.5s ease;
    position: relative;
    overflow: hidden;
    box-shadow: 0 0 10px rgba(99, 102, 241, 0.3);
}

.aibpg-progress-bar-inner::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
        45deg,
        rgba(255, 255, 255, 0.2) 25%,
        transparent 25%,
        transparent 50%,
        rgba(255, 255, 255, 0.2) 50%,
        rgba(255, 255, 255, 0.2) 75%,
        transparent 75%,
        transparent
    );
    background-size: 20px 20px;
    animation: aibpg-progress-animation 2s linear infinite;
    z-index: 1;
}

@keyframes aibpg-progress-animation {
    0% { background-position: 0 0; }
    100% { background-position: 40px 0; }
}

.aibpg-progress-percentage {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: var(--aibpg-gray-800);
    font-size: var(--aibpg-font-size-xs);
    font-weight: 600;
    text-shadow: 0 0 3px rgba(255, 255, 255, 0.9);
    z-index: 2;
}

.aibpg-saving-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(15, 23, 42, 0.85);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    backdrop-filter: blur(8px);
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.aibpg-saving-spinner {
    width: 80px;
    height: 80px;
    position: relative;
    margin-bottom: var(--aibpg-spacing-8);
}

.aibpg-saving-spinner:before,
.aibpg-saving-spinner:after {
    content: '';
    position: absolute;
    border-radius: var(--aibpg-border-radius-full);
}

.aibpg-saving-spinner:before {
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, var(--aibpg-primary-400) 0%, var(--aibpg-primary-600) 100%);
    animation: aibpg-pulse 2s ease-out infinite;
    box-shadow: 0 0 30px rgba(99, 102, 241, 0.5);
}

.aibpg-saving-spinner:after {
    width: 80%;
    height: 80%;
    background-color: rgba(15, 23, 42, 0.85);
    top: 10%;
    left: 10%;
    backdrop-filter: blur(8px);
}

.aibpg-saving-message {
    color: white;
    font-size: var(--aibpg-font-size-2xl);
    text-align: center;
    max-width: 80%;
    line-height: 1.6;
    margin-bottom: var(--aibpg-spacing-5);
    font-weight: 600;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
    font-family: var(--aibpg-font-family-heading);
    letter-spacing: var(--aibpg-letter-spacing-wide);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 0.8; }
    50% { opacity: 1; }
    100% { opacity: 0.8; }
}

.aibpg-saving-submessage {
    color: rgba(255, 255, 255, 0.8);
    font-size: var(--aibpg-font-size-base);
    text-align: center;
    max-width: 80%;
    line-height: 1.6;
}

/* Style for the close button in the overlay */
.aibpg-saving-overlay .aibpg-button {
    background: rgba(255, 255, 255, 0.2) !important;
    border: 2px solid white !important;
    color: white !important;
    padding: 10px 25px !important;
    font-weight: 600 !important;
    transition: all 0.3s ease !important;
    margin-top: var(--aibpg-spacing-6);
}

.aibpg-saving-overlay .aibpg-button:hover {
    background: white !important;
    color: var(--aibpg-primary-600) !important;
    transform: translateY(-3px) !important;
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2) !important;
}

@keyframes aibpg-loading {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes aibpg-pulse {
    0% { transform: scale(0.95); opacity: 0.8; }
    50% { transform: scale(1.05); opacity: 1; }
    100% { transform: scale(0.95); opacity: 0.8; }
}

/* Fade-in animation for model selector container */
.aibpg-fade-in {
    animation: aibpg-fade-in 0.5s cubic-bezier(0.34, 1.56, 0.64, 1);
    transform-origin: center top;
}

@keyframes aibpg-fade-in {
    from { opacity: 0; transform: translateY(-10px) scale(0.95); }
    to { opacity: 1; transform: translateY(0) scale(1); }
}

/* Table of contents */
.aibpg-toc {
    background: var(--aibpg-primary-50);
    padding: var(--aibpg-spacing-6);
    margin: var(--aibpg-spacing-8) 0;
    border-radius: var(--aibpg-border-radius-lg);
    box-shadow: var(--aibpg-shadow-md);
    position: relative;
    border: 1px solid var(--aibpg-primary-200);
    border-left: 4px solid var(--aibpg-primary-500);
}

.aibpg-toc::before {
    content: "\f203"; /* Dashicon: list-view */
    font-family: dashicons;
    position: absolute;
    top: -16px;
    left: 20px;
    background: linear-gradient(135deg, var(--aibpg-primary-500), var(--aibpg-primary-600));
    color: white;
    width: 32px;
    height: 32px;
    border-radius: var(--aibpg-border-radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    box-shadow: var(--aibpg-shadow-md);
}

.aibpg-toc h4 {
    margin-top: 0;
    margin-bottom: var(--aibpg-spacing-4);
    color: var(--aibpg-primary-700);
    font-size: var(--aibpg-font-size-lg);
    font-weight: 600;
    padding-bottom: var(--aibpg-spacing-3);
    border-bottom: 1px solid var(--aibpg-primary-200);
}

.aibpg-toc ul {
    margin: 0;
    padding-left: var(--aibpg-spacing-5);
    list-style-type: none;
}

.aibpg-toc li {
    margin-bottom: var(--aibpg-spacing-3);
    position: relative;
}

.aibpg-toc li a {
    color: var(--aibpg-gray-700);
    text-decoration: none;
    transition: var(--aibpg-transition);
    display: inline-block;
    padding: var(--aibpg-spacing-1) 0;
    font-size: var(--aibpg-font-size-sm);
    font-weight: 500;
}

.aibpg-toc li a:hover {
    color: var(--aibpg-primary-600);
    transform: translateX(3px);
}

.aibpg-toc li::before {
    content: "";
    position: absolute;
    left: -15px;
    top: 10px;
    width: 6px;
    height: 6px;
    border-radius: var(--aibpg-border-radius-full);
    background-color: var(--aibpg-primary-500);
}

.aibpg-toc .toc-level-2 {
    margin-left: var(--aibpg-spacing-4);
}

.aibpg-toc .toc-level-2::before {
    background-color: var(--aibpg-secondary-500);
    width: 5px;
    height: 5px;
}

.aibpg-toc .toc-level-3 {
    margin-left: var(--aibpg-spacing-8);
}

.aibpg-toc .toc-level-3::before {
    background-color: var(--aibpg-gray-500);
    width: 4px;
    height: 4px;
}

.aibpg-toc .toc-level-4 {
    margin-left: 45px;
}

.aibpg-toc .toc-level-4::before {
    background-color: var(--aibpg-gray-400);
    width: 3px;
    height: 3px;
}

/* Notifications */
.aibpg-notification-container {
    position: fixed;
    top: 60px;
    right: 30px;
    z-index: 9999;
    width: 380px;
    display: flex;
    flex-direction: column;
    gap: var(--aibpg-spacing-3);
    pointer-events: none; /* Allow clicking through the container */
}

.aibpg-notification {
    pointer-events: auto; /* Re-enable pointer events for individual notifications */
    padding: var(--aibpg-spacing-5);
    border-radius: var(--aibpg-border-radius-lg);
    box-shadow: var(--aibpg-shadow-xl);
    transform: translateX(120%) scale(0.95);
    transition: transform 0.5s cubic-bezier(0.34, 1.56, 0.64, 1), opacity 0.3s ease;
    opacity: 0;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    position: relative;
    overflow: hidden;
    border: 1px solid transparent;
    backdrop-filter: blur(4px);
}

.aibpg-notification::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
}

.aibpg-notification-show {
    transform: translateX(0) scale(1);
    opacity: 1;
}

.aibpg-notification-message {
    flex: 1;
    padding-right: var(--aibpg-spacing-4);
    line-height: 1.6;
    position: relative;
    font-size: var(--aibpg-font-size-sm);
    font-weight: 500;
    display: flex;
    align-items: center;
}

.aibpg-notification-message .dashicons {
    margin-right: var(--aibpg-spacing-3);
    font-size: 20px;
    width: 20px;
    height: 20px;
}

.aibpg-notification-close {
    cursor: pointer;
    font-size: 20px;
    font-weight: bold;
    color: rgba(0, 0, 0, 0.3);
    transition: var(--aibpg-transition);
    padding: 0 var(--aibpg-spacing-1);
    margin: -5px -5px 0 0;
    line-height: 1;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: var(--aibpg-border-radius-full);
}

.aibpg-notification-close:hover {
    color: rgba(0, 0, 0, 0.6);
    transform: rotate(90deg);
    background: rgba(0, 0, 0, 0.05);
}

/* Progress bar for notifications */
.aibpg-notification-progress {
    position: absolute;
    bottom: 0;
    left: 0;
    height: 3px;
    background: rgba(255, 255, 255, 0.5);
    width: 100%;
}

.aibpg-notification-success {
    background-color: var(--aibpg-success-light);
    color: var(--aibpg-success);
    border-color: rgba(16, 185, 129, 0.2);
}

.aibpg-notification-success::before {
    background: linear-gradient(to bottom, var(--aibpg-success), var(--aibpg-success));
}

.aibpg-notification-success .aibpg-notification-progress {
    background: var(--aibpg-success);
    opacity: 0.3;
}

.aibpg-notification-error {
    background-color: var(--aibpg-danger-light);
    color: var(--aibpg-danger);
    border-color: rgba(239, 68, 68, 0.2);
}

.aibpg-notification-error::before {
    background: linear-gradient(to bottom, var(--aibpg-danger), var(--aibpg-danger));
}

.aibpg-notification-error .aibpg-notification-progress {
    background: var(--aibpg-danger);
    opacity: 0.3;
}

.aibpg-notification-info {
    background-color: var(--aibpg-info-light);
    color: var(--aibpg-info);
    border-color: rgba(59, 130, 246, 0.2);
}

.aibpg-notification-info::before {
    background: linear-gradient(to bottom, var(--aibpg-info), var(--aibpg-info));
}

.aibpg-notification-info .aibpg-notification-progress {
    background: var(--aibpg-info);
    opacity: 0.3;
}

.aibpg-notification-warning {
    background-color: var(--aibpg-warning-light);
    color: var(--aibpg-warning);
    border-color: rgba(245, 158, 11, 0.2);
}

.aibpg-notification-warning::before {
    background: linear-gradient(to bottom, var(--aibpg-warning), var(--aibpg-warning));
}

.aibpg-notification-warning .aibpg-notification-progress {
    background: var(--aibpg-warning);
    opacity: 0.3;
}

/* Meta information */
.aibpg-meta-info {
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid var(--aibpg-gray-200);
    color: var(--aibpg-gray-600);
    font-size: 14px;
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
}

.aibpg-meta-item {
    display: inline-flex;
    align-items: center;
}

.aibpg-meta-item::before {
    font-family: dashicons;
    font-size: 16px;
    margin-right: 5px;
    color: var(--aibpg-primary);
}

.aibpg-meta-date::before {
    content: "\f508"; /* Dashicon: calendar */
}

.aibpg-meta-author::before {
    content: "\f110"; /* Dashicon: admin-users */
}

.aibpg-meta-keyword::before {
    content: "\f323"; /* Dashicon: tag */
}

.aibpg-meta-model::before {
    content: "\f14b"; /* Dashicon: admin-settings */
}

/* Content actions */
.aibpg-content-actions {
    margin: 20px 0;
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
}

.aibpg-content-actions .button {
    padding: 8px 16px;
    height: auto;
    line-height: 1.5;
    font-size: 14px;
    font-weight: 500;
    transition: var(--aibpg-transition);
    display: inline-flex;
    align-items: center;
}

.aibpg-content-actions .button::before {
    font-family: dashicons;
    font-size: 16px;
    margin-right: 5px;
}

.aibpg-content-actions .button-regenerate::before {
    content: "\f463"; /* Dashicon: update */
}

.aibpg-content-actions .button-edit::before {
    content: "\f464"; /* Dashicon: edit */
}

.aibpg-content-actions .button-copy::before {
    content: "\f105"; /* Dashicon: admin-page */
}

/* Model selector */
.aibpg-model-selector {
    margin-top: 15px;
    max-width: 400px;
    position: relative;
}

.aibpg-model-selector label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: var(--aibpg-gray-700);
    font-size: 14px;
}

.aibpg-model-selector select {
    width: 100%;
    padding: 10px 15px;
    border: 1px solid var(--aibpg-gray-300);
    border-radius: 4px;
    background-color: var(--aibpg-white);
    color: var(--aibpg-gray-700);
    font-size: 14px;
    line-height: 1.5;
    transition: var(--aibpg-transition);
    appearance: none;
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="%236c757d" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M6 9l6 6 6-6"/></svg>');
    background-repeat: no-repeat;
    background-position: right 15px center;
    background-size: 16px;
    padding-right: 40px;
}

.aibpg-model-selector select:focus {
    border-color: var(--aibpg-primary);
    box-shadow: 0 0 0 1px var(--aibpg-primary);
    outline: none;
}

.aibpg-model-selector-loading {
    display: inline-block;
    margin-left: 10px;
    vertical-align: middle;
}

.aibpg-model-info {
    margin-top: 10px;
    padding: 12px 15px;
    background-color: var(--aibpg-primary-light);
    border-radius: 4px;
    font-size: 13px;
    color: var(--aibpg-gray-700);
    line-height: 1.5;
    display: flex;
    align-items: center;
}

.aibpg-model-info::before {
    content: "\f348"; /* Dashicon: info */
    font-family: dashicons;
    font-size: 16px;
    margin-right: 8px;
    color: var(--aibpg-primary);
}

/* Responsive adjustments */
@media screen and (max-width: 782px) {
    .aibpg-dashboard-header {
        flex-direction: column;
        padding: 25px;
    }

    .aibpg-dashboard-welcome h2 {
        font-size: 24px;
    }

    .aibpg-dashboard-stats {
        margin-top: 25px;
        justify-content: flex-start;
        flex-wrap: wrap;
    }

    .aibpg-stat-box {
        min-width: 120px;
        flex: 1;
    }

    .aibpg-generate-container {
        flex-direction: column;
    }

    .aibpg-notification-container {
        width: calc(100% - 30px);
        right: 15px;
        top: 50px;
    }

    .aibpg-form-section {
        padding: 20px;
    }

    .aibpg-tab-content {
        padding: 20px;
    }

    .aibpg-action-box {
        min-width: 100%;
    }

    .nav-tab {
        margin-right: 15px;
        padding: 10px 5px;
        font-size: 14px;
    }
}

/* Dark mode toggle */
.aibpg-dark-mode-toggle-wrapper {
    position: absolute;
    top: 20px;
    right: 20px;
    z-index: 10;
}

.aibpg-dark-mode-toggle {
    display: inline-flex;
    align-items: center;
    cursor: pointer;
}

.aibpg-dark-mode-toggle input {
    opacity: 0;
    width: 0;
    height: 0;
    position: absolute;
}

.aibpg-dark-mode-slider {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
    background-color: var(--aibpg-gray-300);
    border-radius: 34px;
    transition: var(--aibpg-transition);
    margin-right: 10px;
}

.aibpg-dark-mode-slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    border-radius: 50%;
    transition: var(--aibpg-transition);
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" fill="%23f59e0b"><path d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" /></svg>');
    background-repeat: no-repeat;
    background-position: center;
}

input:checked + .aibpg-dark-mode-slider {
    background-color: var(--aibpg-primary);
}

input:checked + .aibpg-dark-mode-slider:before {
    transform: translateX(26px);
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" fill="%234a6cf7"><path d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z" /></svg>');
}

.aibpg-dark-mode-label {
    font-size: 14px;
    font-weight: 500;
    color: var(--aibpg-gray-700);
}

/* Dark mode support */
body.wp-admin.aibpg-dark-mode {
    --aibpg-primary: #6d8eff;
    --aibpg-primary-dark: #5a75e6;
    --aibpg-primary-light: #1e2a4a;
    --aibpg-white: #1f2937;
    --aibpg-gray-100: #374151;
    --aibpg-gray-200: #4b5563;
    --aibpg-gray-300: #6b7280;
    --aibpg-gray-400: #9ca3af;
    --aibpg-gray-500: #d1d5db;
    --aibpg-gray-600: #e5e7eb;
    --aibpg-gray-700: #f3f4f6;
    --aibpg-gray-800: #f9fafb;
    --aibpg-gray-900: #ffffff;
    --aibpg-box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
}

body.wp-admin.aibpg-dark-mode .aibpg-notification-success {
    background-color: rgba(46, 125, 50, 0.2);
}

body.wp-admin.aibpg-dark-mode .aibpg-notification-error {
    background-color: rgba(198, 40, 40, 0.2);
}

body.wp-admin.aibpg-dark-mode .aibpg-notification-info {
    background-color: rgba(2, 119, 189, 0.2);
}

body.wp-admin.aibpg-dark-mode .aibpg-notification-warning {
    background-color: rgba(245, 124, 0, 0.2);
}

body.wp-admin.aibpg-dark-mode .aibpg-preview-body pre,
body.wp-admin.aibpg-dark-mode .aibpg-preview-body code {
    background: var(--aibpg-gray-100);
}
