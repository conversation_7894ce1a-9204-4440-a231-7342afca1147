<?php
/**
 * Admin class
 *
 * @package Blog_Post_Generator_Pro
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Admin class
 */
class BPG_Admin {
    /**
     * Constructor
     */
    public function __construct() {
        $this->init_hooks();
    }

    /**
     * Initialize hooks
     */
    private function init_hooks() {
        // Add admin menu
        add_action('admin_menu', array($this, 'add_admin_menu'));
        
        // Enqueue admin scripts and styles
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_scripts'));
        
        // Add settings link to plugins page
        add_filter('plugin_action_links_' . BPG_PLUGIN_BASENAME, array($this, 'add_settings_link'));
    }

    /**
     * Add admin menu
     */
    public function add_admin_menu() {
        add_menu_page(
            __('Blog Post Generator Pro', 'blog-post-generator-pro'),
            __('Blog Generator', 'blog-post-generator-pro'),
            'manage_options',
            'blog-post-generator-pro',
            array($this, 'render_admin_page'),
            'dashicons-edit',
            30
        );
    }

    /**
     * Enqueue admin scripts and styles
     *
     * @param string $hook Current admin page
     */
    public function enqueue_admin_scripts($hook) {
        if ($hook != 'toplevel_page_blog-post-generator-pro') {
            return;
        }
        
        // Enqueue WordPress media library
        wp_enqueue_media();
        
        // Enqueue WordPress color picker
        wp_enqueue_style('wp-color-picker');
        wp_enqueue_script('wp-color-picker');
        
        // Enqueue select2
        wp_enqueue_style('select2', BPG_PLUGIN_URL . 'assets/css/select2.min.css', array(), '4.1.0');
        wp_enqueue_script('select2', BPG_PLUGIN_URL . 'assets/js/select2.min.js', array('jquery'), '4.1.0', true);
        
        // Enqueue custom styles and scripts
        wp_enqueue_style('bpg-admin-style', BPG_PLUGIN_URL . 'assets/css/admin.css', array(), BPG_VERSION);
        wp_enqueue_script('bpg-admin-script', BPG_PLUGIN_URL . 'assets/js/admin.js', array('jquery', 'wp-color-picker', 'select2'), BPG_VERSION, true);
        
        // Localize script with data
        wp_localize_script('bpg-admin-script', 'bpg_params', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('bpg_nonce'),
            'researching_text' => __('Researching...', 'blog-post-generator-pro'),
            'generating_text' => __('Generating...', 'blog-post-generator-pro'),
            'saving_text' => __('Saving...', 'blog-post-generator-pro'),
            'plugin_url' => BPG_PLUGIN_URL,
            'current_user' => wp_get_current_user()->display_name
        ));
    }

    /**
     * Add settings link to plugins page
     *
     * @param array $links Plugin action links
     * @return array
     */
    public function add_settings_link($links) {
        $settings_link = '<a href="' . admin_url('admin.php?page=blog-post-generator-pro') . '">' . __('Settings', 'blog-post-generator-pro') . '</a>';
        array_unshift($links, $settings_link);
        return $links;
    }

    /**
     * Render admin page
     */
    public function render_admin_page() {
        // Get categories
        $categories = get_categories(array(
            'hide_empty' => false,
            'orderby' => 'name',
            'order' => 'ASC'
        ));
        
        // Get tags
        $tags = get_tags(array(
            'hide_empty' => false,
            'orderby' => 'name',
            'order' => 'ASC'
        ));
        
        // Include admin template
        include_once BPG_PLUGIN_DIR . 'templates/admin.php';
    }
}
