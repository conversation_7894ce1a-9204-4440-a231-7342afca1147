<div class="wrap pbg-admin-wrap">
    <h1 class="pbg-page-title">
        <span class="dashicons dashicons-edit"></span> Professional Blog Generator
        <span class="pbg-version">v<?php echo PBG_VERSION; ?></span>
    </h1>
    
    <div class="pbg-tabs">
        <div class="nav-tab-wrapper pbg-tab-nav">
            <a href="#auto-research" class="nav-tab nav-tab-active" id="tab-auto-research">
                <span class="dashicons dashicons-search"></span> Auto Research
            </a>
            <a href="#manual-keyword" class="nav-tab" id="tab-manual-keyword">
                <span class="dashicons dashicons-edit-page"></span> Manual Keyword
            </a>
            <a href="#settings" class="nav-tab" id="tab-settings">
                <span class="dashicons dashicons-admin-settings"></span> Settings
            </a>
        </div>
        
        <!-- Auto Research Tab -->
        <div class="pbg-tab-content active" id="auto-research">
            <div class="postbox">
                <div class="inside">
                    <h2><span class="dashicons dashicons-search"></span> Keyword Research</h2>
                    <p>Enter a niche to research keywords for your blog post. Our advanced algorithm will find the best keywords with high search volume and low competition.</p>
                    
                    <div class="pbg-form-field">
                        <label for="pbg-niche">Niche:</label>
                        <input type="text" id="pbg-niche" class="regular-text" placeholder="e.g., health-fitness, nutrition, technology">
                        <p class="description">Enter a broad topic or industry to find relevant keywords.</p>
                    </div>
                    
                    <div class="pbg-form-actions">
                        <button id="pbg-research-button" class="button button-primary">
                            <span class="dashicons dashicons-search"></span> Research Keywords
                        </button>
                    </div>
                    
                    <div id="pbg-research-results" style="display: none;">
                        <h3>Research Results</h3>
                        <div class="pbg-results-table-wrapper">
                            <table class="widefat striped">
                                <thead>
                                    <tr>
                                        <th>Keyword</th>
                                        <th>Search Volume</th>
                                        <th>Difficulty</th>
                                        <th>CPC</th>
                                        <th>Competition</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>
                                <tbody id="pbg-keywords-list">
                                    <!-- Keywords will be added here dynamically -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Manual Keyword Tab -->
        <div class="pbg-tab-content" id="manual-keyword" style="display: none;">
            <div class="postbox">
                <div class="inside">
                    <h2><span class="dashicons dashicons-edit-page"></span> Manual Keyword Entry</h2>
                    <p>Enter a keyword manually to generate a blog post. Customize the tone, length, and other settings to create the perfect content for your audience.</p>
                    
                    <div class="pbg-form-field">
                        <label for="pbg-keyword">Keyword:</label>
                        <input type="text" id="pbg-keyword" class="regular-text" placeholder="e.g., weight loss tips, healthy recipes">
                        <p class="description">Enter the main keyword for your blog post.</p>
                    </div>
                    
                    <div class="pbg-form-row">
                        <div class="pbg-form-field pbg-form-field-half">
                            <label for="pbg-category">Category:</label>
                            <select id="pbg-category" class="regular-text">
                                <option value="0">Select a category</option>
                                <?php foreach ($categories as $category) : ?>
                                    <option value="<?php echo esc_attr($category->term_id); ?>"><?php echo esc_html($category->name); ?></option>
                                <?php endforeach; ?>
                            </select>
                            <p class="description">Select the category for your blog post.</p>
                        </div>
                        
                        <div class="pbg-form-field pbg-form-field-half">
                            <label for="pbg-post-status">Post Status:</label>
                            <select id="pbg-post-status" class="regular-text">
                                <option value="draft">Draft</option>
                                <option value="publish">Publish</option>
                                <option value="pending">Pending Review</option>
                                <option value="private">Private</option>
                            </select>
                            <p class="description">Set the status for your blog post.</p>
                        </div>
                    </div>
                    
                    <div class="pbg-form-row">
                        <div class="pbg-form-field pbg-form-field-half">
                            <label for="pbg-tone">Tone:</label>
                            <select id="pbg-tone" class="regular-text">
                                <option value="professional">Professional</option>
                                <option value="casual">Casual</option>
                                <option value="humorous">Humorous</option>
                                <option value="educational">Educational</option>
                                <option value="persuasive">Persuasive</option>
                            </select>
                            <p class="description">Select the tone for your blog post.</p>
                        </div>
                        
                        <div class="pbg-form-field pbg-form-field-half">
                            <label for="pbg-length">Length:</label>
                            <select id="pbg-length" class="regular-text">
                                <option value="short">Short (~500 words)</option>
                                <option value="medium" selected>Medium (~1000 words)</option>
                                <option value="long">Long (~1500+ words)</option>
                            </select>
                            <p class="description">Select the desired length for your blog post.</p>
                        </div>
                    </div>
                    
                    <div class="pbg-form-field">
                        <label for="pbg-tags">Tags:</label>
                        <select id="pbg-tags" class="regular-text" multiple>
                            <?php foreach ($tags as $tag) : ?>
                                <option value="<?php echo esc_attr($tag->term_id); ?>"><?php echo esc_html($tag->name); ?></option>
                            <?php endforeach; ?>
                        </select>
                        <p class="description">Select tags for your blog post (optional).</p>
                    </div>
                    
                    <div class="pbg-form-actions">
                        <button id="pbg-generate-button" class="button button-primary">
                            <span class="dashicons dashicons-welcome-write-blog"></span> Generate Blog Post
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Settings Tab -->
        <div class="pbg-tab-content" id="settings" style="display: none;">
            <div class="postbox">
                <div class="inside">
                    <h2><span class="dashicons dashicons-admin-settings"></span> Plugin Settings</h2>
                    <p>Customize the plugin settings to match your preferences and requirements.</p>
                    
                    <div class="pbg-form-field">
                        <label for="pbg-default-author">Default Author:</label>
                        <select id="pbg-default-author" class="regular-text">
                            <?php
                            $users = get_users(array('role__in' => array('administrator', 'editor', 'author')));
                            foreach ($users as $user) {
                                echo '<option value="' . esc_attr($user->ID) . '">' . esc_html($user->display_name) . '</option>';
                            }
                            ?>
                        </select>
                        <p class="description">Select the default author for generated blog posts.</p>
                    </div>
                    
                    <div class="pbg-form-field">
                        <label for="pbg-seo-optimization">SEO Optimization:</label>
                        <select id="pbg-seo-optimization" class="regular-text">
                            <option value="basic">Basic</option>
                            <option value="advanced" selected>Advanced</option>
                            <option value="expert">Expert</option>
                        </select>
                        <p class="description">Select the level of SEO optimization for your blog posts.</p>
                    </div>
                    
                    <div class="pbg-form-field">
                        <label for="pbg-internal-links">Number of Internal Links:</label>
                        <input type="number" id="pbg-internal-links" class="small-text" value="6" min="0" max="10">
                        <p class="description">Set the number of internal links to include in each blog post.</p>
                    </div>
                    
                    <div class="pbg-form-field">
                        <label for="pbg-featured-image">Auto-Generate Featured Image:</label>
                        <input type="checkbox" id="pbg-featured-image" checked>
                        <p class="description">Automatically generate and set a featured image for each blog post.</p>
                    </div>
                    
                    <div class="pbg-form-actions">
                        <button id="pbg-save-settings-button" class="button button-primary">
                            <span class="dashicons dashicons-saved"></span> Save Settings
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Preview Section -->
    <div id="pbg-preview" class="postbox" style="display: none;">
        <div class="inside">
            <h2><span class="dashicons dashicons-welcome-write-blog"></span> Generated Blog Post</h2>
            
            <div class="pbg-preview-content">
                <div class="pbg-preview-title">
                    <h3 id="pbg-preview-title"></h3>
                </div>
                
                <div class="pbg-preview-body" id="pbg-preview-body">
                    <!-- Generated content will be added here -->
                </div>
                
                <div id="pbg-featured-image-suggestions" style="display: none;">
                    <h3>Featured Image Suggestions</h3>
                    <ul id="pbg-image-suggestions-list">
                        <!-- Image suggestions will be added here -->
                    </ul>
                </div>
                
                <div class="pbg-preview-actions">
                    <button id="pbg-save-button" class="button button-primary">
                        <span class="dashicons dashicons-saved"></span> Save as WordPress Post
                    </button>
                    <button id="pbg-copy-button" class="button">
                        <span class="dashicons dashicons-clipboard"></span> Copy to Clipboard
                    </button>
                    <button id="pbg-edit-button" class="button">
                        <span class="dashicons dashicons-edit"></span> Edit Content
                    </button>
                    <button id="pbg-reset-button" class="button">
                        <span class="dashicons dashicons-dismiss"></span> Reset
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <div id="pbg-notification-container"></div>
</div>

<script type="text/javascript">
jQuery(document).ready(function($) {
    // Tab navigation
    $('.nav-tab').on('click', function(e) {
        e.preventDefault();
        
        var target = $(this).attr('href').substring(1);
        
        // Remove active class from all tabs
        $('.nav-tab').removeClass('nav-tab-active');
        $('.pbg-tab-content').hide();
        
        // Add active class to clicked tab
        $(this).addClass('nav-tab-active');
        $('#' + target).show();
    });
    
    // Research button click handler
    $('#pbg-research-button').on('click', function() {
        var niche = $('#pbg-niche').val().trim();
        
        if (!niche) {
            alert('Please enter a niche to research');
            return;
        }
        
        // Show loading state
        $(this).prop('disabled', true).text('Researching...');
        
        // Simulate AJAX request
        setTimeout(function() {
            // Generate sample keywords based on niche
            var keywords = [];
            
            if (niche.toLowerCase().includes('health') || niche.toLowerCase().includes('fitness')) {
                keywords = [
                    {keyword: 'best home workouts', search_volume: '12,000', difficulty: 'Medium', cpc: '$1.20', competition: 'Moderate'},
                    {keyword: 'weight loss tips', search_volume: '22,000', difficulty: 'High', cpc: '$2.50', competition: 'High'},
                    {keyword: 'healthy breakfast ideas', search_volume: '8,500', difficulty: 'Low', cpc: '$0.80', competition: 'Low'},
                    {keyword: 'intermittent fasting benefits', search_volume: '9,200', difficulty: 'Medium', cpc: '$1.50', competition: 'Moderate'}
                ];
            } else if (niche.toLowerCase().includes('nutrition') || niche.toLowerCase().includes('food')) {
                keywords = [
                    {keyword: 'protein-rich foods', search_volume: '14,500', difficulty: 'Medium', cpc: '$1.30', competition: 'Moderate'},
                    {keyword: 'keto diet plan', search_volume: '18,000', difficulty: 'High', cpc: '$2.20', competition: 'High'},
                    {keyword: 'vegan protein sources', search_volume: '7,800', difficulty: 'Low', cpc: '$0.90', competition: 'Low'},
                    {keyword: 'best vitamins for energy', search_volume: '6,300', difficulty: 'Medium', cpc: '$1.40', competition: 'Moderate'}
                ];
            } else {
                keywords = [
                    {keyword: niche + ' tips and tricks', search_volume: '5,000', difficulty: 'Low', cpc: '$0.75', competition: 'Low'},
                    {keyword: 'best ' + niche + ' strategies', search_volume: '7,500', difficulty: 'Medium', cpc: '$1.10', competition: 'Moderate'},
                    {keyword: 'how to improve ' + niche, search_volume: '10,000', difficulty: 'High', cpc: '$1.80', competition: 'High'}
                ];
            }
            
            // Populate keywords table
            var $tbody = $('#pbg-keywords-list');
            $tbody.empty();
            
            $.each(keywords, function(index, keyword) {
                var row = '<tr>';
                row += '<td>' + keyword.keyword + '</td>';
                row += '<td>' + keyword.search_volume + '</td>';
                row += '<td>' + keyword.difficulty + '</td>';
                row += '<td>' + keyword.cpc + '</td>';
                row += '<td>' + keyword.competition + '</td>';
                row += '<td><button class="button pbg-select-keyword" data-keyword="' + keyword.keyword + '">Select</button></td>';
                row += '</tr>';
                
                $tbody.append(row);
            });
            
            // Show results
            $('#pbg-research-results').fadeIn();
            
            // Reset button state
            $('#pbg-research-button').prop('disabled', false).text('Research Keywords');
        }, 1500);
    });
    
    // Keyword selection from research results
    $(document).on('click', '.pbg-select-keyword', function() {
        var keyword = $(this).data('keyword');
        
        // Switch to manual keyword tab
        $('#tab-manual-keyword').trigger('click');
        
        // Set the keyword
        $('#pbg-keyword').val(keyword);
    });
    
    // Generate button click handler
    $('#pbg-generate-button').on('click', function() {
        var keyword = $('#pbg-keyword').val().trim();
        
        if (!keyword) {
            alert('Please enter a keyword');
            return;
        }
        
        // Show loading state
        $(this).prop('disabled', true).text('Generating...');
        
        // Simulate AJAX request
        setTimeout(function() {
            // Generate sample blog post
            var title = 'The Ultimate Guide to ' + keyword.charAt(0).toUpperCase() + keyword.slice(1);
            var content = '<h2>Introduction</h2>';
            content += '<p>Welcome to our comprehensive guide on ' + keyword + '. In this article, we\'ll explore everything you need to know about this topic.</p>';
            content += '<h2>What is ' + keyword.charAt(0).toUpperCase() + keyword.slice(1) + '?</h2>';
            content += '<p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nullam auctor, nisl eget ultricies tincidunt, nisl nisl aliquam nisl, eget ultricies nisl nisl eget nisl.</p>';
            content += '<h2>Benefits of ' + keyword.charAt(0).toUpperCase() + keyword.slice(1) + '</h2>';
            content += '<ul>';
            content += '<li>Benefit 1: Lorem ipsum dolor sit amet</li>';
            content += '<li>Benefit 2: Consectetur adipiscing elit</li>';
            content += '<li>Benefit 3: Nullam auctor, nisl eget ultricies tincidunt</li>';
            content += '</ul>';
            content += '<h2>How to Get Started with ' + keyword.charAt(0).toUpperCase() + keyword.slice(1) + '</h2>';
            content += '<p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nullam auctor, nisl eget ultricies tincidunt, nisl nisl aliquam nisl, eget ultricies nisl nisl eget nisl.</p>';
            content += '<h3>Key Takeaways</h3>';
            content += '<p>Remember these important points about ' + keyword + ':</p>';
            content += '<ul>';
            content += '<li>Key point 1: Lorem ipsum dolor sit amet</li>';
            content += '<li>Key point 2: Consectetur adipiscing elit</li>';
            content += '<li>Key point 3: Nullam auctor, nisl eget ultricies tincidunt</li>';
            content += '</ul>';
            content += '<h2>Conclusion</h2>';
            content += '<p>Now that you understand ' + keyword + ', you can start implementing these strategies in your daily life.</p>';
            
            // Populate preview
            $('#pbg-preview-title').text(title);
            $('#pbg-preview-body').html(content);
            
            // Show preview
            $('#pbg-preview').fadeIn();
            
            // Scroll to preview
            $('html, body').animate({
                scrollTop: $('#pbg-preview').offset().top - 50
            }, 500);
            
            // Reset button state
            $('#pbg-generate-button').prop('disabled', false).text('Generate Blog Post');
            
            // Show image suggestions
            $('#pbg-image-suggestions-list').empty();
            $('#pbg-image-suggestions-list').append('<li>A professional image related to ' + keyword + '</li>');
            $('#pbg-image-suggestions-list').append('<li>An infographic showing the benefits of ' + keyword + '</li>');
            $('#pbg-image-suggestions-list').append('<li>A step-by-step visual guide for ' + keyword + '</li>');
            $('#pbg-featured-image-suggestions').fadeIn();
        }, 2000);
    });
    
    // Save button click handler
    $('#pbg-save-button').on('click', function() {
        var title = $('#pbg-preview-title').text();
        var content = $('#pbg-preview-body').html();
        var category = $('#pbg-category').val();
        var postStatus = $('#pbg-post-status').val();
        
        if (!title || !content) {
            alert('No content to save');
            return;
        }
        
        // Show loading state
        $(this).prop('disabled', true).text('Saving...');
        
        // Simulate AJAX request
        setTimeout(function() {
            alert('Blog post saved successfully as a ' + postStatus + ' in WordPress');
            
            // Reset button state
            $('#pbg-save-button').prop('disabled', false).text('Save as WordPress Post');
        }, 1500);
    });
    
    // Copy button click handler
    $('#pbg-copy-button').on('click', function() {
        var title = $('#pbg-preview-title').text();
        var content = $('#pbg-preview-body').html();
        
        // Create a temporary textarea element
        var textarea = document.createElement('textarea');
        textarea.value = title + '\n\n' + content;
        document.body.appendChild(textarea);
        
        // Select and copy the text
        textarea.select();
        document.execCommand('copy');
        
        // Remove the textarea
        document.body.removeChild(textarea);
        
        alert('Content copied to clipboard');
    });
    
    // Reset button click handler
    $('#pbg-reset-button').on('click', function() {
        // Hide preview
        $('#pbg-preview').fadeOut();
        
        // Clear form fields
        $('#pbg-keyword').val('');
        $('#pbg-niche').val('');
        
        // Hide research results
        $('#pbg-research-results').hide();
        
        // Hide image suggestions
        $('#pbg-featured-image-suggestions').hide();
    });
    
    // Save settings button click handler
    $('#pbg-save-settings-button').on('click', function() {
        // Show loading state
        $(this).prop('disabled', true).text('Saving...');
        
        // Simulate AJAX request
        setTimeout(function() {
            alert('Settings saved successfully');
            
            // Reset button state
            $('#pbg-save-settings-button').prop('disabled', false).text('Save Settings');
        }, 1000);
    });
});
</script>
