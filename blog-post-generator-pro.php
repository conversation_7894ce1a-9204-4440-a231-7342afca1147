<?php
/**
 * Plugin Name: Blog Post Generator Pro
 * Plugin URI: https://example.com/blog-post-generator-pro
 * Description: A premium blog post generator with advanced keyword research, AI content generation, and SEO optimization
 * Version: 1.0.0
 * Requires at least: 5.6
 * Requires PHP: 7.2
 * Author: Augment Agent
 * Author URI: https://example.com
 * License: GPL v2 or later
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 * Text Domain: blog-post-generator-pro
 * Domain Path: /languages
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('BPG_VERSION', '1.0.0');
define('BPG_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('BPG_PLUGIN_URL', plugin_dir_url(__FILE__));
define('BPG_PLUGIN_BASENAME', plugin_basename(__FILE__));

// Include required files
require_once BPG_PLUGIN_DIR . 'includes/class-admin.php';
require_once BPG_PLUGIN_DIR . 'includes/class-api.php';
require_once BPG_PLUGIN_DIR . 'includes/class-generator.php';

/**
 * Main plugin class
 */
class Blog_Post_Generator_Pro {
    /**
     * The single instance of the class
     *
     * @var Blog_Post_Generator_Pro
     */
    protected static $_instance = null;

    /**
     * Admin class instance
     *
     * @var BPG_Admin
     */
    public $admin;

    /**
     * API class instance
     *
     * @var BPG_API
     */
    public $api;

    /**
     * Generator class instance
     *
     * @var BPG_Generator
     */
    public $generator;

    /**
     * Main Blog_Post_Generator_Pro Instance
     *
     * Ensures only one instance of Blog_Post_Generator_Pro is loaded or can be loaded.
     *
     * @return Blog_Post_Generator_Pro - Main instance
     */
    public static function instance() {
        if (is_null(self::$_instance)) {
            self::$_instance = new self();
        }
        return self::$_instance;
    }

    /**
     * Constructor
     */
    public function __construct() {
        $this->init_hooks();
        $this->init_classes();
    }

    /**
     * Initialize hooks
     */
    private function init_hooks() {
        // Activation hook
        register_activation_hook(__FILE__, array($this, 'activate'));
        
        // Deactivation hook
        register_deactivation_hook(__FILE__, array($this, 'deactivate'));
        
        // Load text domain
        add_action('plugins_loaded', array($this, 'load_textdomain'));
    }

    /**
     * Initialize classes
     */
    private function init_classes() {
        // Initialize admin class
        $this->admin = new BPG_Admin();
        
        // Initialize API class
        $this->api = new BPG_API();
        
        // Initialize generator class
        $this->generator = new BPG_Generator();
    }

    /**
     * Activation hook
     */
    public function activate() {
        // Create necessary directories
        $this->create_directories();
        
        // Set default options
        $this->set_default_options();
        
        // Flush rewrite rules
        flush_rewrite_rules();
    }

    /**
     * Deactivation hook
     */
    public function deactivate() {
        // Flush rewrite rules
        flush_rewrite_rules();
    }

    /**
     * Load text domain
     */
    public function load_textdomain() {
        load_plugin_textdomain('blog-post-generator-pro', false, dirname(plugin_basename(__FILE__)) . '/languages');
    }

    /**
     * Create necessary directories
     */
    private function create_directories() {
        $upload_dir = wp_upload_dir();
        $bpg_dir = $upload_dir['basedir'] . '/bpg';
        
        if (!file_exists($bpg_dir)) {
            wp_mkdir_p($bpg_dir);
        }
    }

    /**
     * Set default options
     */
    private function set_default_options() {
        $default_options = array(
            'seo_optimization' => 'advanced',
            'internal_links' => 6,
            'featured_image' => true,
            'default_author' => get_current_user_id(),
            'default_tone' => 'professional',
            'default_length' => 'medium'
        );
        
        foreach ($default_options as $option => $value) {
            if (get_option('bpg_' . $option) === false) {
                update_option('bpg_' . $option, $value);
            }
        }
    }
}

/**
 * Returns the main instance of Blog_Post_Generator_Pro
 *
 * @return Blog_Post_Generator_Pro
 */
function BPG() {
    return Blog_Post_Generator_Pro::instance();
}

// Initialize the plugin
BPG();
