<?php
/**
 * Plugin Name: Final Blog Generator
 * Plugin URI: https://example.com/final-blog-generator
 * Description: A feature-rich blog post generator with keyword research and content generation
 * Version: 1.0.0
 * Author: Augment Agent
 * Author URI: https://example.com
 * Text Domain: final-blog-generator
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

// Add admin menu
add_action('admin_menu', 'fbg_add_menu');

function fbg_add_menu() {
    add_menu_page(
        'Blog Post Generator',
        'Blog Generator',
        'manage_options',
        'final-blog-generator',
        'fbg_admin_page',
        'dashicons-edit',
        30
    );
}

// Admin page content
function fbg_admin_page() {
    // Get categories
    $categories = get_categories(array(
        'hide_empty' => false,
        'orderby' => 'name',
        'order' => 'ASC'
    ));
    ?>
    <div class="wrap">
        <h1>Blog Post Generator</h1>
        
        <div class="nav-tab-wrapper">
            <a href="#auto-research" class="nav-tab nav-tab-active" id="tab-auto-research">Auto Research</a>
            <a href="#manual-keyword" class="nav-tab" id="tab-manual-keyword">Manual Keyword</a>
        </div>
        
        <div class="tab-content" id="auto-research" style="display: block; margin-top: 20px;">
            <div class="postbox">
                <div class="inside">
                    <h2>Keyword Research</h2>
                    <p>Enter a niche to research keywords for your blog post.</p>
                    
                    <div class="form-field" style="margin-bottom: 15px;">
                        <label for="fbg-niche" style="display: block; margin-bottom: 5px; font-weight: bold;">Niche:</label>
                        <input type="text" id="fbg-niche" class="regular-text" placeholder="e.g., health-fitness, nutrition, technology">
                    </div>
                    
                    <div class="form-field">
                        <button id="fbg-research-button" class="button button-primary">Research Keywords</button>
                    </div>
                    
                    <div id="fbg-research-results" style="display: none; margin-top: 20px;">
                        <h3>Research Results</h3>
                        <div class="fbg-results-table-wrapper">
                            <table class="widefat striped">
                                <thead>
                                    <tr>
                                        <th>Keyword</th>
                                        <th>Search Volume</th>
                                        <th>Difficulty</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>
                                <tbody id="fbg-keywords-list">
                                    <!-- Keywords will be added here dynamically -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="tab-content" id="manual-keyword" style="display: none; margin-top: 20px;">
            <div class="postbox">
                <div class="inside">
                    <h2>Manual Keyword Entry</h2>
                    <p>Enter a keyword manually to generate a blog post.</p>
                    
                    <div class="form-field" style="margin-bottom: 15px;">
                        <label for="fbg-keyword" style="display: block; margin-bottom: 5px; font-weight: bold;">Keyword:</label>
                        <input type="text" id="fbg-keyword" class="regular-text" placeholder="e.g., weight loss tips, healthy recipes">
                    </div>
                    
                    <div class="form-field" style="margin-bottom: 15px;">
                        <label for="fbg-category" style="display: block; margin-bottom: 5px; font-weight: bold;">Category:</label>
                        <select id="fbg-category" class="regular-text">
                            <option value="0">Select a category</option>
                            <?php foreach ($categories as $category) : ?>
                                <option value="<?php echo esc_attr($category->term_id); ?>"><?php echo esc_html($category->name); ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <div class="form-field" style="margin-bottom: 15px;">
                        <label for="fbg-post-status" style="display: block; margin-bottom: 5px; font-weight: bold;">Post Status:</label>
                        <select id="fbg-post-status" class="regular-text">
                            <option value="draft">Draft</option>
                            <option value="publish">Publish</option>
                        </select>
                    </div>
                    
                    <div class="form-field">
                        <button id="fbg-generate-button" class="button button-primary">Generate Blog Post</button>
                    </div>
                </div>
            </div>
        </div>
        
        <div id="fbg-preview" class="postbox" style="display: none; margin-top: 20px;">
            <div class="inside">
                <h2>Generated Blog Post</h2>
                
                <div class="fbg-preview-content">
                    <div class="fbg-preview-title">
                        <h3 id="fbg-preview-title"></h3>
                    </div>
                    
                    <div class="fbg-preview-body" id="fbg-preview-body">
                        <!-- Generated content will be added here -->
                    </div>
                    
                    <div class="fbg-preview-actions" style="margin-top: 20px;">
                        <button id="fbg-save-button" class="button button-primary">Save as WordPress Post</button>
                        <button id="fbg-copy-button" class="button">Copy to Clipboard</button>
                        <button id="fbg-reset-button" class="button">Reset</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        jQuery(document).ready(function($) {
            // Tab navigation
            $('.nav-tab').on('click', function(e) {
                e.preventDefault();
                
                var target = $(this).attr('href').substring(1);
                
                // Remove active class from all tabs
                $('.nav-tab').removeClass('nav-tab-active');
                $('.tab-content').hide();
                
                // Add active class to clicked tab
                $(this).addClass('nav-tab-active');
                $('#' + target).show();
                
                // If switching to manual keyword tab, focus the keyword field
                if (target === 'manual-keyword') {
                    setTimeout(function() {
                        $('#fbg-keyword').focus();
                    }, 400);
                }
            });
            
            // Research button click handler
            $('#fbg-research-button').on('click', function() {
                var niche = $('#fbg-niche').val().trim();
                
                if (!niche) {
                    alert('Please enter a niche to research');
                    return;
                }
                
                // Show loading state
                $(this).prop('disabled', true).text('Researching...');
                
                // Simulate AJAX request with setTimeout
                setTimeout(function() {
                    // Generate sample keywords based on niche
                    var keywords = [];
                    
                    if (niche.toLowerCase().includes('health') || niche.toLowerCase().includes('fitness')) {
                        keywords = [
                            {keyword: 'best home workouts', search_volume: '12,000', difficulty: 'Medium'},
                            {keyword: 'weight loss tips', search_volume: '22,000', difficulty: 'High'},
                            {keyword: 'healthy breakfast ideas', search_volume: '8,500', difficulty: 'Low'},
                            {keyword: 'intermittent fasting benefits', search_volume: '9,200', difficulty: 'Medium'}
                        ];
                    } else if (niche.toLowerCase().includes('nutrition') || niche.toLowerCase().includes('food')) {
                        keywords = [
                            {keyword: 'protein-rich foods', search_volume: '14,500', difficulty: 'Medium'},
                            {keyword: 'keto diet plan', search_volume: '18,000', difficulty: 'High'},
                            {keyword: 'vegan protein sources', search_volume: '7,800', difficulty: 'Low'},
                            {keyword: 'best vitamins for energy', search_volume: '6,300', difficulty: 'Medium'}
                        ];
                    } else {
                        keywords = [
                            {keyword: niche + ' tips and tricks', search_volume: '5,000', difficulty: 'Low'},
                            {keyword: 'best ' + niche + ' strategies', search_volume: '7,500', difficulty: 'Medium'},
                            {keyword: 'how to improve ' + niche, search_volume: '10,000', difficulty: 'High'}
                        ];
                    }
                    
                    // Populate keywords table
                    var $tbody = $('#fbg-keywords-list');
                    $tbody.empty();
                    
                    $.each(keywords, function(index, keyword) {
                        var row = '<tr>';
                        row += '<td>' + keyword.keyword + '</td>';
                        row += '<td>' + keyword.search_volume + '</td>';
                        row += '<td>' + keyword.difficulty + '</td>';
                        row += '<td><button class="button fbg-select-keyword" data-keyword="' + keyword.keyword + '">Select</button></td>';
                        row += '</tr>';
                        
                        $tbody.append(row);
                    });
                    
                    // Show results
                    $('#fbg-research-results').fadeIn();
                    
                    // Reset button state
                    $('#fbg-research-button').prop('disabled', false).text('Research Keywords');
                }, 1500);
            });
            
            // Keyword selection from research results
            $(document).on('click', '.fbg-select-keyword', function() {
                var keyword = $(this).data('keyword');
                
                // Switch to manual keyword tab
                $('#tab-manual-keyword').trigger('click');
                
                // Set the keyword
                $('#fbg-keyword').val(keyword);
            });
            
            // Generate button click handler
            $('#fbg-generate-button').on('click', function() {
                var keyword = $('#fbg-keyword').val().trim();
                
                if (!keyword) {
                    alert('Please enter a keyword');
                    return;
                }
                
                // Show loading state
                $(this).prop('disabled', true).text('Generating...');
                
                // Simulate AJAX request with setTimeout
                setTimeout(function() {
                    // Generate sample blog post
                    var title = 'The Ultimate Guide to ' + keyword.charAt(0).toUpperCase() + keyword.slice(1);
                    var content = '<h2>Introduction</h2>';
                    content += '<p>Welcome to our comprehensive guide on ' + keyword + '. In this article, we\'ll explore everything you need to know about this topic.</p>';
                    content += '<h2>What is ' + keyword.charAt(0).toUpperCase() + keyword.slice(1) + '?</h2>';
                    content += '<p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nullam auctor, nisl eget ultricies tincidunt, nisl nisl aliquam nisl, eget ultricies nisl nisl eget nisl.</p>';
                    content += '<h2>Benefits of ' + keyword.charAt(0).toUpperCase() + keyword.slice(1) + '</h2>';
                    content += '<ul>';
                    content += '<li>Benefit 1: Lorem ipsum dolor sit amet</li>';
                    content += '<li>Benefit 2: Consectetur adipiscing elit</li>';
                    content += '<li>Benefit 3: Nullam auctor, nisl eget ultricies tincidunt</li>';
                    content += '</ul>';
                    content += '<h2>How to Get Started with ' + keyword.charAt(0).toUpperCase() + keyword.slice(1) + '</h2>';
                    content += '<p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nullam auctor, nisl eget ultricies tincidunt, nisl nisl aliquam nisl, eget ultricies nisl nisl eget nisl.</p>';
                    content += '<h3>Key Takeaways</h3>';
                    content += '<p>Remember these important points about ' + keyword + ':</p>';
                    content += '<ul>';
                    content += '<li>Key point 1: Lorem ipsum dolor sit amet</li>';
                    content += '<li>Key point 2: Consectetur adipiscing elit</li>';
                    content += '<li>Key point 3: Nullam auctor, nisl eget ultricies tincidunt</li>';
                    content += '</ul>';
                    content += '<h2>Conclusion</h2>';
                    content += '<p>Now that you understand ' + keyword + ', you can start implementing these strategies in your daily life.</p>';
                    
                    // Populate preview
                    $('#fbg-preview-title').text(title);
                    $('#fbg-preview-body').html(content);
                    
                    // Show preview
                    $('#fbg-preview').fadeIn();
                    
                    // Scroll to preview
                    $('html, body').animate({
                        scrollTop: $('#fbg-preview').offset().top - 50
                    }, 500);
                    
                    // Reset button state
                    $('#fbg-generate-button').prop('disabled', false).text('Generate Blog Post');
                }, 2000);
            });
            
            // Save button click handler
            $('#fbg-save-button').on('click', function() {
                var title = $('#fbg-preview-title').text();
                var content = $('#fbg-preview-body').html();
                var category = $('#fbg-category').val();
                var postStatus = $('#fbg-post-status').val();
                
                if (!title || !content) {
                    alert('No content to save');
                    return;
                }
                
                // Show loading state
                $(this).prop('disabled', true).text('Saving...');
                
                // Simulate AJAX request with setTimeout
                setTimeout(function() {
                    alert('Blog post saved successfully as a ' + postStatus + ' in WordPress');
                    
                    // Reset button state
                    $('#fbg-save-button').prop('disabled', false).text('Save as WordPress Post');
                }, 1500);
            });
            
            // Copy button click handler
            $('#fbg-copy-button').on('click', function() {
                var title = $('#fbg-preview-title').text();
                var content = $('#fbg-preview-body').html();
                
                // Create a temporary textarea element
                var textarea = document.createElement('textarea');
                textarea.value = title + '\n\n' + content;
                document.body.appendChild(textarea);
                
                // Select and copy the text
                textarea.select();
                document.execCommand('copy');
                
                // Remove the textarea
                document.body.removeChild(textarea);
                
                alert('Content copied to clipboard');
            });
            
            // Reset button click handler
            $('#fbg-reset-button').on('click', function() {
                // Hide preview
                $('#fbg-preview').fadeOut();
                
                // Clear form fields
                $('#fbg-keyword').val('');
                $('#fbg-niche').val('');
                
                // Hide research results
                $('#fbg-research-results').hide();
            });
        });
    </script>
    <?php
}
