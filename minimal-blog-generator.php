<?php
/**
 * Plugin Name: Minimal Blog Generator
 * Plugin URI: https://example.com/minimal-blog-generator
 * Description: A minimal blog post generator with tab navigation
 * Version: 1.0.0
 * Author: Augment Agent
 * Author URI: https://example.com
 * Text Domain: minimal-blog-generator
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

// Add admin menu
add_action('admin_menu', 'mbg_add_menu');

function mbg_add_menu() {
    add_menu_page(
        'Minimal Blog Generator',
        'Blog Generator',
        'manage_options',
        'minimal-blog-generator',
        'mbg_admin_page',
        'dashicons-edit',
        30
    );
}

// Admin page content
function mbg_admin_page() {
    ?>
    <div class="wrap">
        <h1>Minimal Blog Generator</h1>
        
        <div class="nav-tab-wrapper">
            <a href="#auto-research" class="nav-tab nav-tab-active">Auto Research</a>
            <a href="#manual-keyword" class="nav-tab">Manual Keyword</a>
        </div>
        
        <div class="tab-content" id="auto-research" style="display: block;">
            <div class="postbox">
                <div class="inside">
                    <h2>Keyword Research</h2>
                    <p>Enter a niche to research keywords for your blog post.</p>
                    
                    <div class="form-field">
                        <label for="mbg-niche">Niche:</label>
                        <input type="text" id="mbg-niche" placeholder="e.g., health-fitness, nutrition, technology">
                    </div>
                    
                    <div class="form-field">
                        <button id="mbg-research-button" class="button button-primary">Research Keywords</button>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="tab-content" id="manual-keyword" style="display: none;">
            <div class="postbox">
                <div class="inside">
                    <h2>Manual Keyword Entry</h2>
                    <p>Enter a keyword manually to generate a blog post.</p>
                    
                    <div class="form-field">
                        <label for="mbg-keyword">Keyword:</label>
                        <input type="text" id="mbg-keyword" placeholder="e.g., weight loss tips, healthy recipes">
                    </div>
                    
                    <div class="form-field">
                        <button id="mbg-generate-button" class="button button-primary">Generate Blog Post</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        jQuery(document).ready(function($) {
            // Tab navigation
            $('.nav-tab').on('click', function(e) {
                e.preventDefault();
                
                var target = $(this).attr('href').substring(1);
                
                // Remove active class from all tabs
                $('.nav-tab').removeClass('nav-tab-active');
                $('.tab-content').hide();
                
                // Add active class to clicked tab
                $(this).addClass('nav-tab-active');
                $('#' + target).show();
                
                // If switching to manual keyword tab, focus the keyword field
                if (target === 'manual-keyword') {
                    setTimeout(function() {
                        $('#mbg-keyword').focus();
                    }, 400);
                }
            });
            
            // Research button click handler
            $('#mbg-research-button').on('click', function() {
                var niche = $('#mbg-niche').val().trim();
                
                if (!niche) {
                    alert('Please enter a niche to research');
                    return;
                }
                
                alert('Researching keywords for: ' + niche);
            });
            
            // Generate button click handler
            $('#mbg-generate-button').on('click', function() {
                var keyword = $('#mbg-keyword').val().trim();
                
                if (!keyword) {
                    alert('Please enter a keyword');
                    return;
                }
                
                alert('Generating blog post for: ' + keyword);
            });
        });
    </script>
    <?php
}
