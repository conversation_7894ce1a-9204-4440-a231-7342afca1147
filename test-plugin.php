<?php
/**
 * Plugin Name: Test Plugin
 * Plugin URI: https://example.com/test-plugin
 * Description: A simple test plugin to diagnose issues
 * Version: 1.0.0
 * Author: Augment Agent
 * Author URI: https://example.com
 * Text Domain: test-plugin
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('TEST_PLUGIN_VERSION', '1.0.0');
define('TEST_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('TEST_PLUGIN_URL', plugin_dir_url(__FILE__));

// Add admin menu
add_action('admin_menu', 'test_plugin_add_menu');

function test_plugin_add_menu() {
    add_menu_page(
        'Test Plugin',
        'Test Plugin',
        'manage_options',
        'test-plugin',
        'test_plugin_admin_page',
        'dashicons-admin-generic',
        99
    );
}

function test_plugin_admin_page() {
    ?>
    <div class="wrap">
        <h1>Test Plugin</h1>
        <p>This is a simple test plugin to diagnose issues.</p>
        <p>Plugin URL: <?php echo TEST_PLUGIN_URL; ?></p>
        <p>Plugin Directory: <?php echo TEST_PLUGIN_DIR; ?></p>
    </div>
    <?php
}

// Add scripts and styles
add_action('admin_enqueue_scripts', 'test_plugin_enqueue_scripts');

function test_plugin_enqueue_scripts($hook) {
    if ($hook != 'toplevel_page_test-plugin') {
        return;
    }
    
    wp_enqueue_style('test-plugin-style', TEST_PLUGIN_URL . 'style.css', array(), TEST_PLUGIN_VERSION);
    wp_enqueue_script('test-plugin-script', TEST_PLUGIN_URL . 'script.js', array('jquery'), TEST_PLUGIN_VERSION, true);
}
