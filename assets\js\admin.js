/**
 * Admin JavaScript for AI Blog Post Generator
 */
(function($) {
    'use strict';

    // Dark mode functionality
    function initDarkMode() {
        const prefersDarkMode = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches;
        const savedMode = localStorage.getItem('aibpg_dark_mode');

        if (savedMode === 'true' || (savedMode === null && prefersDarkMode)) {
            $('body').addClass('aibpg-dark-mode');
        }

        // Add dark mode toggle to header
        if ($('.aibpg-dark-mode-toggle-wrapper').length === 0) {
            const darkModeToggle = `
                <div class="aibpg-dark-mode-toggle-wrapper">
                    <label class="aibpg-dark-mode-toggle">
                        <input type="checkbox" id="aibpg-dark-mode-toggle" ${savedMode === 'true' || (savedMode === null && prefersDarkMode) ? 'checked' : ''}>
                        <span class="aibpg-dark-mode-slider"></span>
                        <span class="aibpg-dark-mode-label">Dark Mode</span>
                    </label>
                </div>
            `;
            $('.aibpg-dashboard-header').append(darkModeToggle);

            // Handle dark mode toggle
            $(document).on('change', '#aibpg-dark-mode-toggle', function() {
                if ($(this).is(':checked')) {
                    $('body').addClass('aibpg-dark-mode');
                    localStorage.setItem('aibpg_dark_mode', 'true');
                } else {
                    $('body').removeClass('aibpg-dark-mode');
                    localStorage.setItem('aibpg_dark_mode', 'false');
                }
            });
        }
    }

    // Initialize when document is ready
    $(document).ready(function() {
        // Initialize dark mode
        initDarkMode();

        // Make sure the first tab is active
        if ($('.aibpg-tab-button').length > 0) {
            // Find the first tab button and make it active
            var firstTabButton = $('.aibpg-tab-button').first();
            var firstTabTarget = firstTabButton.data('tab');

            // Set active classes
            firstTabButton.addClass('active');
            $('#' + firstTabTarget + '-content').addClass('active').show();

            console.log('Initialized first tab:', firstTabTarget);
        }
        // Tab navigation is handled by the enhanced tab navigation below

        // Format API key fields
        $('.aibpg-api-key-field').on('input', function() {
            var field = $(this);
            var value = field.val().trim();

            // Remove any spaces
            if (value.indexOf(' ') !== -1) {
                field.val(value.replace(/\s+/g, ''));
            }
        });

        // API key validation
        $('.aibpg-validate-key').on('click', function(e) {
            e.preventDefault(); // Prevent default form submission

            var button = $(this);
            var service = button.data('service');
            var apiKeyField = $('#aibpg_' + service + '_api_key');
            var apiKey = apiKeyField.val().trim();
            var resultContainer = $('#' + service + '-validation-result');
            var modelSelector = $('#aibpg_' + service + '_preferred_model');
            var selectedModel = modelSelector.length ? modelSelector.val() : '';
            var modelSelectorContainer = modelSelector.closest('.aibpg-model-selector');
            var loadingIndicator = $('<span class="aibpg-model-selector-loading"><span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Loading models...</span>');

            if (!apiKey) {
                resultContainer.html('<div class="aibpg-alert aibpg-alert-danger"><span class="dashicons dashicons-warning"></span> Please enter an API key.</div>').addClass('aibpg-validation-error').removeClass('aibpg-validation-success').show();
                showNotification('error', 'Please enter an API key before validating.', 5000);
                apiKeyField.addClass('aibpg-input-error').focus();
                setTimeout(function() {
                    apiKeyField.removeClass('aibpg-input-error');
                }, 3000);
                return;
            }

            // Show loading state
            button.prop('disabled', true);
            var originalButtonText = button.text();
            button.html('<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> ' + aibpg_params.validating_text);
            resultContainer.html('').removeClass('aibpg-validation-error aibpg-validation-success').hide();

            // Add loading indicator to model selector if it exists
            if (modelSelectorContainer.length) {
                modelSelectorContainer.append(loadingIndicator);
                modelSelector.prop('disabled', true);
            }

            // Log validation attempt
            console.log('Validating API key for service: ' + service);

            // Send AJAX request
            $.ajax({
                url: aibpg_params.ajax_url,
                type: 'POST',
                data: {
                    action: 'aibpg_validate_api_key',
                    service: service,
                    api_key: apiKey,
                    model: selectedModel,
                    nonce: aibpg_params.nonce
                },
                success: function(response) {
                    if (response.success) {
                        // Success message with icon
                        resultContainer.html('<div class="aibpg-alert aibpg-alert-success"><span class="dashicons dashicons-yes-alt"></span> ' + response.data.message + '</div>')
                            .addClass('aibpg-validation-success')
                            .removeClass('aibpg-validation-error')
                            .show();

                        // Show success notification
                        showNotification('success', 'API key validated successfully!', 3000);

                        // Update the API key field with masked version if provided
                        if (response.data.masked_key) {
                            apiKeyField.val(response.data.masked_key);
                            apiKeyField.addClass('aibpg-input-success');
                            setTimeout(function() {
                                apiKeyField.removeClass('aibpg-input-success');
                            }, 3000);
                        }

                        // Update model selector if models are provided
                        if (response.data.models && modelSelector.length) {
                            updateModelSelector(modelSelector, response.data.models);

                            // Enable the model selector with animation
                            modelSelector.prop('disabled', false).addClass('aibpg-input-success');
                            setTimeout(function() {
                                modelSelector.removeClass('aibpg-input-success');
                            }, 3000);

                            // Show model selector container with animation
                            var modelSelectorContainer = modelSelector.closest('.aibpg-form-field');
                            if (modelSelectorContainer.length) {
                                modelSelectorContainer.addClass('aibpg-fade-in');
                            }
                        }

                        // Set the API key status for this service to true regardless of models
                        $('#aibpg-api-key-status-' + service).attr('data-configured', 'true');
                    } else {
                        // Error message with icon
                        resultContainer.html('<div class="aibpg-alert aibpg-alert-danger"><span class="dashicons dashicons-warning"></span> ' + response.data.message + '</div>')
                            .addClass('aibpg-validation-error')
                            .removeClass('aibpg-validation-success')
                            .show();

                        // Show error notification
                        showNotification('error', 'API key validation failed: ' + response.data.message, 5000);

                        // Highlight input field
                        apiKeyField.addClass('aibpg-input-error');
                        setTimeout(function() {
                            apiKeyField.removeClass('aibpg-input-error');
                        }, 3000);

                        // Update model selector if models are provided despite validation failure
                        if (response.data.models && modelSelector.length) {
                            updateModelSelector(modelSelector, response.data.models);
                        }

                        // If locked out, disable the button for a longer period
                        if (response.data.locked) {
                            button.prop('disabled', true);
                            setTimeout(function() {
                                button.prop('disabled', false);
                                button.html(originalButtonText);
                            }, 60000); // 1 minute

                            // Show countdown timer
                            var countdownContainer = $('<div class="aibpg-countdown">Try again in <span class="aibpg-countdown-timer">60</span> seconds</div>');
                            resultContainer.append(countdownContainer);

                            var countdown = 60;
                            var countdownTimer = setInterval(function() {
                                countdown--;
                                $('.aibpg-countdown-timer').text(countdown);
                                if (countdown <= 0) {
                                    clearInterval(countdownTimer);
                                    countdownContainer.remove();
                                }
                            }, 1000);
                        }
                    }
                },
                error: function(xhr, status, error) {
                    var errorMessage = 'Network error during validation. Please try again.';

                    // Try to get more detailed error message if available
                    if (xhr.responseJSON && xhr.responseJSON.data && xhr.responseJSON.data.message) {
                        errorMessage = xhr.responseJSON.data.message;
                    } else if (xhr.status) {
                        errorMessage += ' (Status: ' + xhr.status + ')';
                    }

                    // Error message with icon and details
                    resultContainer.html('<div class="aibpg-alert aibpg-alert-danger"><span class="dashicons dashicons-warning"></span> An error occurred while validating the API key: ' + errorMessage + '</div>')
                        .addClass('aibpg-validation-error')
                        .removeClass('aibpg-validation-success')
                        .show();

                    // Show error notification with details
                    showNotification('error', 'Validation error: ' + errorMessage, 5000);

                    // Highlight input field
                    apiKeyField.addClass('aibpg-input-error');
                    setTimeout(function() {
                        apiKeyField.removeClass('aibpg-input-error');
                    }, 3000);

                    // Log error for debugging
                    console.error('API validation error:', {xhr: xhr, status: status, error: error});
                },
                complete: function() {
                    // Remove loading indicator
                    loadingIndicator.remove();

                    // Re-enable button and model selector if not locked
                    // The response variable is not accessible in the complete handler
                    // So we need to check if the button is still disabled
                    if (button.prop('disabled')) {
                        // Check if we're in a locked state by looking at the DOM
                        var isLocked = $('.aibpg-countdown').length > 0;

                        if (!isLocked) {
                            button.prop('disabled', false);
                            button.html(originalButtonText);
                        }
                    }

                    // Re-enable model selector
                    if (modelSelector.length && !modelSelector.hasClass('aibpg-model-selector-loading')) {
                        modelSelector.prop('disabled', false);
                    }
                }
            });
        });

        // Update model selector with available models
        function updateModelSelector(selector, models) {
            var currentValue = selector.val();
            selector.empty();

            // Add default option
            selector.append($('<option>', {
                value: '',
                text: 'Select a model'
            }));

            // Add available models
            $.each(models, function(id, name) {
                selector.append($('<option>', {
                    value: id,
                    text: name,
                    selected: (id === currentValue)
                }));
            });

            // Enable the selector
            selector.prop('disabled', false);
        }

        // Modern notification system with enhanced animations and interactions
        function showNotification(type, message, duration) {
            // Default duration is 5 seconds
            duration = duration || 5000;

            // Define icons based on notification type
            var icons = {
                'success': '<span class="dashicons dashicons-yes-alt"></span>',
                'error': '<span class="dashicons dashicons-dismiss"></span>',
                'warning': '<span class="dashicons dashicons-warning"></span>',
                'info': '<span class="dashicons dashicons-info"></span>'
            };

            var notificationClass = 'aibpg-notification-' + type;
            var notification = $(
                '<div class="aibpg-notification ' + notificationClass + '">' +
                '<div class="aibpg-notification-message">' + icons[type] + message + '</div>' +
                '<div class="aibpg-notification-close">&times;</div>' +
                '</div>'
            );

            // Add to container with stacking effect
            var container = $('.aibpg-notification-container');
            if (!container.length) {
                container = $('<div class="aibpg-notification-container"></div>');
                $('body').append(container);
            }

            // Add notification
            container.append(notification);

            // Add click handler to close button with rotation effect
            notification.find('.aibpg-notification-close').on('click', function() {
                dismissNotification(notification);
            });

            // Add click handler to the entire notification for better UX
            notification.on('click', function(e) {
                if (!$(e.target).hasClass('aibpg-notification-close') &&
                    !$(e.target).closest('a, button').length) {
                    // Highlight effect on click
                    notification.css('transform', 'scale(0.98)');
                    setTimeout(function() {
                        notification.css('transform', 'scale(1)');
                    }, 150);
                }
            });

            // Animate in with advanced effects
            setTimeout(function() {
                notification.addClass('aibpg-notification-show');

                // Add subtle bounce animation
                setTimeout(function() {
                    notification.css({
                        'transform': 'translateX(-5px)',
                        'transition': 'transform 0.2s cubic-bezier(0.34, 1.56, 0.64, 1)'
                    });
                    setTimeout(function() {
                        notification.css('transform', 'translateX(0)');
                    }, 150);
                }, 300);
            }, 10);

            // Progress bar for auto-dismiss
            if (duration > 0) {
                var progressBar = $('<div class="aibpg-notification-progress"></div>');
                notification.append(progressBar);

                // Animate progress bar
                progressBar.css({
                    'width': '100%',
                    'transition': 'width ' + duration + 'ms linear'
                });

                setTimeout(function() {
                    progressBar.css('width', '0%');
                }, 50);

                // Remove after delay
                setTimeout(function() {
                    // Only remove if it's still in the DOM
                    if (notification.parent().length) {
                        dismissNotification(notification);
                    }
                }, duration);
            }

            // Helper function to dismiss notification with animation
            function dismissNotification(notif) {
                notif.css({
                    'opacity': '0',
                    'transform': 'translateX(100%)',
                    'transition': 'opacity 0.3s ease, transform 0.3s ease'
                });
                setTimeout(function() {
                    notif.remove();
                }, 300);
            }

            return notification;
        }

        // Enhanced tab navigation with smooth transitions
        $('.aibpg-tab-button').on('click', function(e) {
            e.preventDefault();

            var target = $(this).data('tab');
            var $targetContent = $('#' + target + '-content');

            console.log('Tab clicked:', target);

            // If already active, do nothing
            if ($(this).hasClass('active')) {
                return;
            }

            // Remove active class from all tabs
            $('.aibpg-tab-button').removeClass('active');

            // Add active class to clicked tab with a slight delay for visual effect
            $(this).addClass('active');

            // Smooth transition between tab contents
            $('.aibpg-tab-content.active').fadeOut(200, function() {
                // Hide all tab contents and remove active class
                $('.aibpg-tab-content').removeClass('active').hide();

                // Show the target content with a fade effect
                $targetContent.addClass('active').fadeIn(300);

                // Add subtle entrance animation to elements inside the tab
                $targetContent.find('.aibpg-form-field').each(function(index) {
                    $(this).css({
                        'opacity': 0,
                        'transform': 'translateY(20px)'
                    }).delay(index * 50).animate({
                        'opacity': 1,
                        'transform': 'translateY(0)'
                    }, 300);
                });

                // If switching to manual keyword tab, focus the keyword field
                if (target === 'manual-keyword') {
                    setTimeout(function() {
                        $('#aibpg-keyword').focus();
                    }, 400);
                }
            });
        });

        // Research keywords
        $('#aibpg-research-button').on('click', function(e) {
            e.preventDefault(); // Prevent default form submission

            var button = $(this);
            var niche = $('#aibpg-niche').val();
            var resultsContainer = $('#aibpg-research-results');
            var keywordsList = $('.aibpg-keywords-list');

            // Validate input
            if (!niche || niche.trim() === '') {
                showNotification('error', 'Please select a niche before researching keywords.', 5000);
                return;
            }

            // Show loading state
            button.prop('disabled', true);
            button.html('<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> ' + aibpg_params.researching_text);
            keywordsList.html('');
            resultsContainer.hide();

            // Send AJAX request
            $.ajax({
                url: aibpg_params.ajax_url,
                type: 'POST',
                data: {
                    action: 'aibpg_research_keywords',
                    niche: niche,
                    nonce: aibpg_params.nonce
                },
                success: function(response) {
                    if (response.success) {
                        // Display keywords
                        var keywords = response.data.keywords;

                        if (keywords.length > 0) {
                            $.each(keywords, function(index, keyword) {
                                var keywordItem = $('<div class="aibpg-keyword-item" data-keyword="' + keyword.keyword + '">');
                                keywordItem.append('<div class="aibpg-keyword-title">' + keyword.keyword + '</div>');
                                keywordItem.append('<div class="aibpg-keyword-meta"><span>Volume: ' + keyword.search_volume + '</span><span>Difficulty: ' + keyword.difficulty + '</span></div>');
                                keywordsList.append(keywordItem);
                            });

                            // Show results
                            resultsContainer.show();

                            // Add click handler for keyword selection with enhanced UX
                            $('.aibpg-keyword-item').on('click', function() {
                                // Visual feedback for selection
                                $('.aibpg-keyword-item').removeClass('selected');
                                $(this).addClass('selected');

                                // Get the keyword
                                var keyword = $(this).data('keyword');
                                var keywordField = $('#aibpg-keyword');

                                // Set the keyword in the manual input with animation
                                keywordField.val(keyword);
                                keywordField.addClass('aibpg-input-success');

                                // Switch to the manual keyword tab
                                $('.aibpg-tab-button[data-tab="manual-keyword"]').click();

                                // Short delay to ensure the tab has switched
                                setTimeout(function() {
                                    // Scroll to the keyword field and focus it
                                    $('html, body').animate({
                                        scrollTop: keywordField.offset().top - 100
                                    }, 500, function() {
                                        keywordField.focus();

                                        // Show a notification
                                        showNotification('success', 'Keyword "' + keyword + '" selected. Click "Generate Blog Post" to continue.', 5000);

                                        // Highlight the generate button
                                        $('#aibpg-generate-button').addClass('aibpg-button-highlight');
                                        setTimeout(function() {
                                            $('#aibpg-generate-button').removeClass('aibpg-button-highlight');
                                        }, 2000);
                                    });
                                }, 100);

                                // Remove success class after a delay
                                setTimeout(function() {
                                    keywordField.removeClass('aibpg-input-success');
                                }, 3000);
                            });
                        } else {
                            keywordsList.html('<p>No keywords found. Please try a different niche.</p>');
                            resultsContainer.show();
                        }
                    } else {
                        alert(response.data.message);
                    }
                },
                error: function() {
                    alert('An error occurred while researching keywords.');
                },
                complete: function() {
                    button.prop('disabled', false);
                    button.text('Research Keywords');
                }
            });
        });

        // Check API key status when AI service changes
        $('#aibpg-ai-service').on('change', function() {
            checkApiKeyStatus();
        });

        // Function to check API key status
        function checkApiKeyStatus() {
            var aiService = $('#aibpg-ai-service').val();
            var apiKeyStatus = $('#aibpg-api-key-status-' + aiService);
            var generateButton = $('#aibpg-generate-button');

            console.log('Checking API key status for ' + aiService);

            if (apiKeyStatus.length && apiKeyStatus.attr('data-configured') === 'true') {
                $('#aibpg-api-key-warning').hide();
                generateButton.prop('disabled', false);
                generateButton.removeClass('disabled');
            } else {
                $('#aibpg-api-key-warning').show();
                generateButton.prop('disabled', true);
                generateButton.addClass('disabled');

                // Show warning notification
                var settingsLink = $('<a href="' + aibpg_params.settings_url + '" class="button">Go to Settings</a>');
                var notification = showNotification('warning', 'You need to set up your API key for ' + aiService + ' in the Settings tab first. ', 10000);
                notification.append(settingsLink);
            }
        }

        // Initial API key status check
        setTimeout(function() {
            checkApiKeyStatus();
        }, 500);

        // Generate blog post
        $('#aibpg-generate-button').on('click', function(e) {
            e.preventDefault(); // Prevent default form submission

            var button = $(this);
            var keyword = $('#aibpg-keyword').val();
            var category = $('#aibpg-category').val();
            var postStatus = $('#aibpg-post-status').val();
            var aiService = $('#aibpg-ai-service').val();
            var previewContainer = $('.aibpg-generate-preview');
            var previewTitle = $('.aibpg-preview-title');
            var previewBody = $('.aibpg-preview-body');
            var progressBar = $('<div class="aibpg-progress-bar"><div class="aibpg-progress-bar-inner"></div></div>');
            var statusMessage = $('<div class="aibpg-status-message">Preparing to generate content...</div>');

            // Validate input
            if (!keyword || keyword.trim() === '') {
                showNotification('error', 'Please enter a keyword or select one from the research results.', 5000);
                $('#aibpg-keyword').addClass('aibpg-input-error').focus();
                setTimeout(function() {
                    $('#aibpg-keyword').removeClass('aibpg-input-error');
                }, 3000);
                return;
            }

            if (!aiService) {
                showNotification('error', 'Please select an AI service.');
                return;
            }

            // Check if API key is configured
            var apiKeyStatus = $('#aibpg-api-key-status-' + aiService);
            if (!apiKeyStatus.length) {
                showNotification('error', 'Unknown AI service selected.');
                return;
            }

            var apiKeyConfigured = apiKeyStatus.attr('data-configured');
            console.log('API key status for ' + aiService + ': ' + apiKeyConfigured);

            if (apiKeyConfigured !== 'true' && apiKeyConfigured !== true) {
                showNotification('error', 'Please configure and validate your API key for the selected AI service first.', 10000);

                // Highlight the settings tab
                var settingsLink = $('<a href="' + aibpg_params.settings_url + '" class="button">Go to Settings</a>');
                var notification = showNotification('warning', 'You need to set up your API key in the Settings tab first. ', 0);
                notification.append(settingsLink);

                return;
            }

            // Show loading state
            button.prop('disabled', true);
            button.text(aibpg_params.generating_text);
            previewContainer.hide();

            // Add progress bar and status message with enhanced UI
            var loadingContainer = $('<div class="aibpg-loading-container"></div>');
            loadingContainer.append(statusMessage);
            loadingContainer.append(progressBar);

            // Add percentage indicator
            var percentageIndicator = $('<div class="aibpg-progress-percentage">0%</div>');
            progressBar.append(percentageIndicator);

            // Add animated icon based on current step
            var stepIcon = $('<div class="aibpg-step-icon"><span class="dashicons dashicons-search"></span></div>');
            loadingContainer.prepend(stepIcon);

            previewContainer.before(loadingContainer);

            // Simulate progress with smoother animation
            var progress = 0;
            var progressInterval = setInterval(function() {
                progress += Math.random() * 1.5;
                if (progress > 95) {
                    progress = 95;
                    clearInterval(progressInterval);
                }

                // Update progress bar width
                var progressWidth = Math.round(progress);
                progressBar.find('.aibpg-progress-bar-inner').css('width', progressWidth + '%');
                percentageIndicator.text(progressWidth + '%');

                // Update status message and icon based on progress
                if (progress < 20) {
                    statusMessage.text('Analyzing keyword and gathering information...');
                    stepIcon.html('<span class="dashicons dashicons-search"></span>');
                } else if (progress < 40) {
                    statusMessage.text('Researching topic and planning content structure...');
                    stepIcon.html('<span class="dashicons dashicons-clipboard"></span>');
                } else if (progress < 60) {
                    statusMessage.text('Drafting content with AI assistance...');
                    stepIcon.html('<span class="dashicons dashicons-edit"></span>');
                } else if (progress < 80) {
                    statusMessage.text('Optimizing content for SEO and readability...');
                    stepIcon.html('<span class="dashicons dashicons-chart-line"></span>');
                } else {
                    statusMessage.text('Finalizing and preparing preview...');
                    stepIcon.html('<span class="dashicons dashicons-welcome-view-site"></span>');
                }
            }, 400);

            // Send AJAX request
            $.ajax({
                url: aibpg_params.ajax_url,
                type: 'POST',
                data: {
                    action: 'aibpg_generate_post',
                    keyword: keyword,
                    category: category,
                    post_status: postStatus,
                    ai_service: aiService,
                    nonce: aibpg_params.nonce
                },
                success: function(response) {
                    clearInterval(progressInterval);
                    progressBar.find('.aibpg-progress-bar-inner').css('width', '100%');
                    statusMessage.text('Content generation complete!');

                    setTimeout(function() {
                        loadingContainer.remove();

                        if (response.success) {
                            // Display preview
                            previewTitle.text(response.data.title);
                            previewBody.html(response.data.content);
                            previewContainer.show();

                            // Store generated content for saving
                            previewContainer.data('title', response.data.title);
                            previewContainer.data('content', response.data.content);

                            // Add word count and other metadata with enhanced display
                            var wordCount = response.data.content.split(/\s+/).length;
                            var currentDate = new Date();
                            var formattedDate = currentDate.toLocaleDateString('en-US', {
                                year: 'numeric',
                                month: 'long',
                                day: 'numeric'
                            });

                            var metaInfo = $('<div class="aibpg-meta-info"></div>');

                            // Add meta items with icons
                            var dateItem = $('<div class="aibpg-meta-item aibpg-meta-date"></div>').text(formattedDate);
                            var authorItem = $('<div class="aibpg-meta-item aibpg-meta-author"></div>').text(aibpg_params.current_user);
                            var keywordItem = $('<div class="aibpg-meta-item aibpg-meta-keyword"></div>').text(keyword);
                            var wordCountItem = $('<div class="aibpg-meta-item"></div>').text(wordCount + ' words');
                            var modelItem = $('<div class="aibpg-meta-item aibpg-meta-model"></div>').text(aiService);

                            metaInfo.append(dateItem, authorItem, keywordItem, wordCountItem, modelItem);
                            previewContainer.prepend(metaInfo);

                            // Show success notification
                            showNotification('success', 'Blog post generated successfully!');
                        } else {
                            showNotification('error', 'Failed to generate blog post: ' + response.data.message);
                        }
                    }, 1000);
                },
                error: function(xhr, status, error) {
                    clearInterval(progressInterval);

                    // Update the loading container to show error state instead of removing it
                    stepIcon.html('<span class="dashicons dashicons-warning" style="color: var(--aibpg-danger-500);"></span>');
                    statusMessage.text('Error generating content');
                    progressBar.find('.aibpg-progress-bar-inner').css({
                        'width': '100%',
                        'background-color': 'var(--aibpg-danger-500)'
                    });

                    // Add retry button
                    var retryButton = $('<button class="aibpg-button" style="margin-top: 20px;">Try Again</button>');
                    loadingContainer.append(retryButton);

                    retryButton.on('click', function() {
                        loadingContainer.remove();
                        $('#aibpg-generate-button').click();
                    });

                    console.error('AJAX Error:', xhr, status, error);

                    var errorMessage = 'An error occurred while generating the blog post.';

                    // Try to extract error message from response
                    if (xhr.responseJSON && xhr.responseJSON.data && xhr.responseJSON.data.message) {
                        errorMessage += ' ' + xhr.responseJSON.data.message;
                    } else if (xhr.responseText) {
                        try {
                            var responseObj = JSON.parse(xhr.responseText);
                            if (responseObj.data && responseObj.data.message) {
                                errorMessage += ' ' + responseObj.data.message;
                            }
                        } catch (e) {
                            console.error('Error parsing response:', e);
                            // If we can't parse the response, include part of the response text
                            if (xhr.responseText.length > 100) {
                                errorMessage += ' Server response: ' + xhr.responseText.substring(0, 100) + '...';
                            } else {
                                errorMessage += ' Server response: ' + xhr.responseText;
                            }
                        }
                    }

                    // Provide more specific error messages based on status code
                    if (xhr.status === 0) {
                        errorMessage = 'Network error. Please check your internet connection and try again.';
                    } else if (xhr.status === 403) {
                        errorMessage = 'Permission denied. Please check your API key and try again.';
                    } else if (xhr.status === 429) {
                        errorMessage = 'Rate limit exceeded. Please wait a moment and try again.';
                    } else if (xhr.status >= 500) {
                        errorMessage = 'Server error. The AI service may be experiencing issues. Please try again later.';
                    } else {
                        // Include HTTP status in the error message
                        errorMessage += ' (Status: ' + xhr.status + ')';
                    }

                    showNotification('error', errorMessage, 10000);
                },
                complete: function() {
                    setTimeout(function() {
                        button.prop('disabled', false);
                        button.text('Generate Blog Post');
                    }, 1000);
                }
            });
        });

        // Handle sitemap analysis
        $('#aibpg-analyze-sitemaps').on('click', function() {
            var button = $(this);
            var statusElement = $('#aibpg-sitemap-analysis-status');
            var sitemapUrls = $('#aibpg_sitemap_urls').val();

            if (!sitemapUrls) {
                showNotification('error', 'Please enter at least one sitemap URL.');
                return;
            }

            // Disable button and show loading status
            button.prop('disabled', true);
            statusElement.html('<span class="spinner is-active"></span> Analyzing sitemaps...');

            // Make AJAX request to analyze sitemaps
            $.ajax({
                url: aibpg_params.ajax_url,
                type: 'POST',
                data: {
                    action: 'aibpg_analyze_sitemaps',
                    nonce: aibpg_params.nonce,
                    sitemap_urls: sitemapUrls
                },
                success: function(response) {
                    if (response.success) {
                        statusElement.html('<span class="dashicons dashicons-yes" style="color: green;"></span> ' + response.data.message);
                        showNotification('success', response.data.message);
                    } else {
                        statusElement.html('<span class="dashicons dashicons-no" style="color: red;"></span> ' + response.data.message);
                        showNotification('error', response.data.message);
                    }
                },
                error: function() {
                    statusElement.html('<span class="dashicons dashicons-no" style="color: red;"></span> Error analyzing sitemaps.');
                    showNotification('error', 'Error analyzing sitemaps. Please try again.');
                },
                complete: function() {
                    button.prop('disabled', false);
                }
            });
        });

        // Save post
        $('#aibpg-save-post').on('click', function() {
            var button = $(this);
            var previewContainer = $('.aibpg-generate-preview');
            var title = previewContainer.data('title');
            var content = previewContainer.data('content');
            var category = $('#aibpg-category').val();
            var postStatus = $('#aibpg-post-status').val();
            var tags = $('#aibpg-tags').val();
            var featuredImage = $('#aibpg-featured-image').val();

            // Validate data
            if (!title || !content) {
                showNotification('error', 'No content to save. Please generate content first.');
                return;
            }

            // Show loading state
            button.prop('disabled', true);
            button.text('Saving...');

            // Add saving overlay
            var overlay = $('<div class="aibpg-saving-overlay"><div class="aibpg-saving-spinner"></div><div class="aibpg-saving-message">Saving your blog post...</div></div>');
            $('body').append(overlay);

            // Send AJAX request
            $.ajax({
                url: aibpg_params.ajax_url,
                type: 'POST',
                data: {
                    action: 'aibpg_save_post',
                    title: title,
                    content: content,
                    category: category,
                    post_status: postStatus,
                    tags: tags,
                    featured_image: featuredImage,
                    nonce: aibpg_params.nonce
                },
                success: function(response) {
                    if (response.success) {
                        // Update overlay message
                        overlay.find('.aibpg-saving-message').text('Blog post saved successfully! Redirecting...');

                        // Show success notification
                        showNotification('success', 'Blog post saved successfully!');

                        // Redirect to edit post page after a short delay
                        setTimeout(function() {
                            if (response.data.edit_url) {
                                window.location.href = response.data.edit_url;
                            } else {
                                window.location.reload();
                            }
                        }, 1500);
                    } else {
                        // Remove overlay
                        overlay.remove();

                        // Show error notification
                        showNotification('error', 'Failed to save blog post: ' + response.data.message);
                    }
                },
                error: function(xhr, status, error) {
                    // Update overlay to show error
                    overlay.find('.aibpg-saving-spinner').html('<span class="dashicons dashicons-warning" style="color: var(--aibpg-danger-500); font-size: 48px;"></span>');
                    overlay.find('.aibpg-saving-message').text('Error saving blog post');

                    // Add close button to overlay
                    var closeButton = $('<button class="aibpg-button" style="margin-top: 20px;">Close</button>');
                    overlay.append(closeButton);

                    closeButton.on('click', function() {
                        overlay.remove();
                    });

                    var errorMessage = 'An error occurred while saving the blog post.';

                    // Try to extract error message from response
                    if (xhr.responseJSON && xhr.responseJSON.data && xhr.responseJSON.data.message) {
                        errorMessage += ' ' + xhr.responseJSON.data.message;
                    } else if (xhr.responseText) {
                        try {
                            var responseObj = JSON.parse(xhr.responseText);
                            if (responseObj.data && responseObj.data.message) {
                                errorMessage += ' ' + responseObj.data.message;
                            }
                        } catch (e) {
                            console.error('Error parsing response:', e);
                        }
                    }

                    // Provide more specific error messages based on status code
                    if (xhr.status === 0) {
                        errorMessage = 'Network error. Please check your internet connection and try again.';
                    } else if (xhr.status === 403) {
                        errorMessage = 'Permission denied. You may not have sufficient privileges to save posts.';
                    } else if (xhr.status >= 500) {
                        errorMessage = 'Server error. WordPress may be experiencing issues. Please try again later.';
                    }

                    showNotification('error', errorMessage, 10000);

                    // Re-enable the button
                    button.prop('disabled', false);
                    button.text('Save Post');
                },
                complete: function() {
                    // Only re-enable if not successful (success case is handled in success callback)
                    if (typeof response === 'undefined' || !response || !response.success) {
                        button.prop('disabled', false);
                        button.text('Save Post');
                    }
                }
            });
        });

        // Regenerate post
        $('#aibpg-regenerate').on('click', function() {
            $('#aibpg-generate-button').click();
        });
    });
})(jQuery);
