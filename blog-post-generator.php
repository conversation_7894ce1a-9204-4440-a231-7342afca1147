<?php
/**
 * Plugin Name: Blog Post Generator
 * Plugin URI: https://example.com/blog-post-generator
 * Description: A simple blog post generator with tab navigation
 * Version: 1.0.0
 * Author: Augment Agent
 * Author URI: https://example.com
 * Text Domain: blog-post-generator
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('BPG_VERSION', '1.0.0');
define('BPG_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('BPG_PLUGIN_URL', plugin_dir_url(__FILE__));

// Add admin menu
add_action('admin_menu', 'bpg_add_menu');

function bpg_add_menu() {
    add_menu_page(
        'Blog Post Generator',
        'Blog Generator',
        'manage_options',
        'blog-post-generator',
        'bpg_admin_page',
        'dashicons-edit',
        30
    );
}

// Enqueue scripts and styles
add_action('admin_enqueue_scripts', 'bpg_enqueue_scripts');

function bpg_enqueue_scripts($hook) {
    if ($hook != 'toplevel_page_blog-post-generator') {
        return;
    }

    // Enqueue styles
    wp_enqueue_style('bpg-admin-style', BPG_PLUGIN_URL . 'assets/css/admin.css', array(), BPG_VERSION);

    // Enqueue scripts
    wp_enqueue_script('bpg-admin-script', BPG_PLUGIN_URL . 'assets/js/admin.js', array('jquery'), BPG_VERSION, true);

    // Localize script with data
    wp_localize_script('bpg-admin-script', 'bpg_params', array(
        'ajax_url' => admin_url('admin-ajax.php'),
        'nonce' => wp_create_nonce('bpg_nonce'),
        'researching_text' => __('Researching...', 'blog-post-generator'),
        'generating_text' => __('Generating...', 'blog-post-generator'),
        'saving_text' => __('Saving...', 'blog-post-generator'),
        'current_user' => wp_get_current_user()->display_name
    ));
}

// Admin page content
function bpg_admin_page() {
    // Get categories
    $categories = get_categories(array(
        'hide_empty' => false,
        'orderby' => 'name',
        'order' => 'ASC'
    ));
    ?>
    <div class="wrap bpg-admin-wrap">
        <h1>Blog Post Generator</h1>

        <div class="bpg-tabs">
            <div class="nav-tab-wrapper">
                <a href="#auto-research" class="nav-tab nav-tab-active" id="tab-auto-research">Auto Research</a>
                <a href="#manual-keyword" class="nav-tab" id="tab-manual-keyword">Manual Keyword</a>
            </div>

            <div class="bpg-tab-content active" id="auto-research">
                <div class="postbox">
                    <div class="inside">
                        <h2>Keyword Research</h2>
                        <p>Enter a niche to research keywords for your blog post.</p>

                        <div class="bpg-form-field">
                            <label for="bpg-niche">Niche:</label>
                            <input type="text" id="bpg-niche" class="regular-text" placeholder="e.g., health-fitness, nutrition, technology">
                        </div>

                        <div class="bpg-form-actions">
                            <button id="bpg-research-button" class="button button-primary">Research Keywords</button>
                        </div>

                        <div id="bpg-research-results" style="display: none;">
                            <h3>Research Results</h3>
                            <div class="bpg-results-table-wrapper">
                                <table class="widefat striped">
                                    <thead>
                                        <tr>
                                            <th>Keyword</th>
                                            <th>Search Volume</th>
                                            <th>Difficulty</th>
                                            <th>Action</th>
                                        </tr>
                                    </thead>
                                    <tbody id="bpg-keywords-list">
                                        <!-- Keywords will be added here dynamically -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bpg-tab-content" id="manual-keyword" style="display: none;">
                <div class="postbox">
                    <div class="inside">
                        <h2>Manual Keyword Entry</h2>
                        <p>Enter a keyword manually to generate a blog post.</p>

                        <div class="bpg-form-field">
                            <label for="bpg-keyword">Keyword:</label>
                            <input type="text" id="bpg-keyword" class="regular-text" placeholder="e.g., weight loss tips, healthy recipes">
                        </div>

                        <div class="bpg-form-field">
                            <label for="bpg-category">Category:</label>
                            <select id="bpg-category" class="regular-text">
                                <option value="0">Select a category</option>
                                <?php foreach ($categories as $category) : ?>
                                    <option value="<?php echo esc_attr($category->term_id); ?>"><?php echo esc_html($category->name); ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="bpg-form-field">
                            <label for="bpg-post-status">Post Status:</label>
                            <select id="bpg-post-status" class="regular-text">
                                <option value="draft">Draft</option>
                                <option value="publish">Publish</option>
                            </select>
                        </div>

                        <div class="bpg-form-actions">
                            <button id="bpg-generate-button" class="button button-primary">Generate Blog Post</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div id="bpg-preview" class="postbox" style="display: none;">
            <div class="inside">
                <h2>Generated Blog Post</h2>

                <div class="bpg-preview-content">
                    <div class="bpg-preview-title">
                        <h3 id="bpg-preview-title"></h3>
                    </div>

                    <div class="bpg-preview-body" id="bpg-preview-body">
                        <!-- Generated content will be added here -->
                    </div>

                    <div class="bpg-preview-actions">
                        <button id="bpg-save-button" class="button button-primary">Save as WordPress Post</button>
                        <button id="bpg-copy-button" class="button">Copy to Clipboard</button>
                        <button id="bpg-reset-button" class="button">Reset</button>
                    </div>
                </div>
            </div>
        </div>

        <div id="bpg-notification-container"></div>
    </div>
    <?php
}

// AJAX handler for keyword research
add_action('wp_ajax_bpg_research_keywords', 'bpg_research_keywords');

function bpg_research_keywords() {
    // Check nonce
    if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'bpg_nonce')) {
        wp_send_json_error(array('message' => 'Invalid nonce'));
    }

    // Check if niche is provided
    if (!isset($_POST['niche']) || empty($_POST['niche'])) {
        wp_send_json_error(array('message' => 'Niche is required'));
    }

    // Get niche
    $niche = sanitize_text_field($_POST['niche']);

    // Simulate keyword research with sample data
    $keywords = array();

    switch ($niche) {
        case 'health-fitness':
            $keywords = array(
                array('keyword' => 'best home workouts', 'search_volume' => '12,000', 'difficulty' => 'Medium'),
                array('keyword' => 'weight loss tips', 'search_volume' => '22,000', 'difficulty' => 'High'),
                array('keyword' => 'healthy breakfast ideas', 'search_volume' => '8,500', 'difficulty' => 'Low'),
                array('keyword' => 'intermittent fasting benefits', 'search_volume' => '9,200', 'difficulty' => 'Medium')
            );
            break;
        case 'nutrition':
            $keywords = array(
                array('keyword' => 'protein-rich foods', 'search_volume' => '14,500', 'difficulty' => 'Medium'),
                array('keyword' => 'keto diet plan', 'search_volume' => '18,000', 'difficulty' => 'High'),
                array('keyword' => 'vegan protein sources', 'search_volume' => '7,800', 'difficulty' => 'Low'),
                array('keyword' => 'best vitamins for energy', 'search_volume' => '6,300', 'difficulty' => 'Medium')
            );
            break;
        default:
            $keywords = array(
                array('keyword' => 'sample keyword 1', 'search_volume' => '5,000', 'difficulty' => 'Low'),
                array('keyword' => 'sample keyword 2', 'search_volume' => '7,500', 'difficulty' => 'Medium'),
                array('keyword' => 'sample keyword 3', 'search_volume' => '10,000', 'difficulty' => 'High')
            );
    }

    // Return keywords
    wp_send_json_success(array('keywords' => $keywords));
}

// AJAX handler for generating blog post
add_action('wp_ajax_bpg_generate_post', 'bpg_generate_post');

function bpg_generate_post() {
    // Check nonce
    if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'bpg_nonce')) {
        wp_send_json_error(array('message' => 'Invalid nonce'));
    }

    // Check if keyword is provided
    if (!isset($_POST['keyword']) || empty($_POST['keyword'])) {
        wp_send_json_error(array('message' => 'Keyword is required'));
    }

    // Get parameters
    $keyword = sanitize_text_field($_POST['keyword']);
    $category = isset($_POST['category']) ? intval($_POST['category']) : 0;
    $post_status = isset($_POST['post_status']) ? sanitize_text_field($_POST['post_status']) : 'draft';

    // Simulate blog post generation with sample data
    $title = 'The Ultimate Guide to ' . ucwords($keyword);
    $content = '<h2>Introduction</h2>';
    $content .= '<p>Welcome to our comprehensive guide on ' . $keyword . '. In this article, we\'ll explore everything you need to know about this topic.</p>';
    $content .= '<h2>What is ' . ucwords($keyword) . '?</h2>';
    $content .= '<p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nullam auctor, nisl eget ultricies tincidunt, nisl nisl aliquam nisl, eget ultricies nisl nisl eget nisl.</p>';
    $content .= '<h2>Benefits of ' . ucwords($keyword) . '</h2>';
    $content .= '<ul>';
    $content .= '<li>Benefit 1: Lorem ipsum dolor sit amet</li>';
    $content .= '<li>Benefit 2: Consectetur adipiscing elit</li>';
    $content .= '<li>Benefit 3: Nullam auctor, nisl eget ultricies tincidunt</li>';
    $content .= '</ul>';
    $content .= '<h2>How to Get Started with ' . ucwords($keyword) . '</h2>';
    $content .= '<p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nullam auctor, nisl eget ultricies tincidunt, nisl nisl aliquam nisl, eget ultricies nisl nisl eget nisl.</p>';
    $content .= '<h3>Key Takeaways</h3>';
    $content .= '<p>Remember these important points about ' . $keyword . ':</p>';
    $content .= '<ul>';
    $content .= '<li>Key point 1: Lorem ipsum dolor sit amet</li>';
    $content .= '<li>Key point 2: Consectetur adipiscing elit</li>';
    $content .= '<li>Key point 3: Nullam auctor, nisl eget ultricies tincidunt</li>';
    $content .= '</ul>';
    $content .= '<h2>Conclusion</h2>';
    $content .= '<p>Now that you understand ' . $keyword . ', you can start implementing these strategies in your daily life.</p>';

    // Return generated content
    wp_send_json_success(array(
        'title' => $title,
        'content' => $content
    ));
}

// AJAX handler for saving blog post
add_action('wp_ajax_bpg_save_post', 'bpg_save_post');

function bpg_save_post() {
    // Check nonce
    if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'bpg_nonce')) {
        wp_send_json_error(array('message' => 'Invalid nonce'));
    }

    // Check if title and content are provided
    if (!isset($_POST['title']) || empty($_POST['title']) || !isset($_POST['content']) || empty($_POST['content'])) {
        wp_send_json_error(array('message' => 'Title and content are required'));
    }

    // Get parameters
    $title = sanitize_text_field($_POST['title']);
    $content = wp_kses_post($_POST['content']);
    $category = isset($_POST['category']) ? intval($_POST['category']) : 0;
    $post_status = isset($_POST['post_status']) ? sanitize_text_field($_POST['post_status']) : 'draft';

    // Create post
    $post_id = wp_insert_post(array(
        'post_title' => $title,
        'post_content' => $content,
        'post_status' => $post_status,
        'post_author' => get_current_user_id(),
        'post_category' => array($category)
    ));

    if (is_wp_error($post_id)) {
        wp_send_json_error(array('message' => $post_id->get_error_message()));
    }

    // Return success
    wp_send_json_success(array(
        'post_id' => $post_id,
        'edit_url' => get_edit_post_link($post_id, 'raw'),
        'view_url' => get_permalink($post_id)
    ));
}

// Create necessary directories on plugin activation
register_activation_hook(__FILE__, 'bpg_activate');

function bpg_activate() {
    // Create directories if they don't exist
    $dirs = array(
        BPG_PLUGIN_DIR . 'assets',
        BPG_PLUGIN_DIR . 'assets/css',
        BPG_PLUGIN_DIR . 'assets/js'
    );

    foreach ($dirs as $dir) {
        if (!file_exists($dir)) {
            wp_mkdir_p($dir);
        }
    }
}
function bpg_admin_page() {
    // Get categories
    $categories = get_categories(array(
        'hide_empty' => false,
        'orderby' => 'name',
        'order' => 'ASC'
    ));

    // Include admin template
    include_once BPG_PLUGIN_DIR . 'templates/admin.php';
}

// AJAX handler for keyword research
add_action('wp_ajax_bpg_research_keywords', 'bpg_research_keywords');

function bpg_research_keywords() {
    // Check nonce
    if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'bpg_nonce')) {
        wp_send_json_error(array('message' => 'Invalid nonce'));
    }

    // Check if niche is provided
    if (!isset($_POST['niche']) || empty($_POST['niche'])) {
        wp_send_json_error(array('message' => 'Niche is required'));
    }

    // Get niche
    $niche = sanitize_text_field($_POST['niche']);

    // Simulate keyword research with sample data
    $keywords = array();

    switch ($niche) {
        case 'health-fitness':
            $keywords = array(
                array('keyword' => 'best home workouts', 'search_volume' => '12,000', 'difficulty' => 'Medium'),
                array('keyword' => 'weight loss tips', 'search_volume' => '22,000', 'difficulty' => 'High'),
                array('keyword' => 'healthy breakfast ideas', 'search_volume' => '8,500', 'difficulty' => 'Low'),
                array('keyword' => 'intermittent fasting benefits', 'search_volume' => '9,200', 'difficulty' => 'Medium')
            );
            break;
        case 'nutrition':
            $keywords = array(
                array('keyword' => 'protein-rich foods', 'search_volume' => '14,500', 'difficulty' => 'Medium'),
                array('keyword' => 'keto diet plan', 'search_volume' => '18,000', 'difficulty' => 'High'),
                array('keyword' => 'vegan protein sources', 'search_volume' => '7,800', 'difficulty' => 'Low'),
                array('keyword' => 'best vitamins for energy', 'search_volume' => '6,300', 'difficulty' => 'Medium')
            );
            break;
        default:
            $keywords = array(
                array('keyword' => 'sample keyword 1', 'search_volume' => '5,000', 'difficulty' => 'Low'),
                array('keyword' => 'sample keyword 2', 'search_volume' => '7,500', 'difficulty' => 'Medium'),
                array('keyword' => 'sample keyword 3', 'search_volume' => '10,000', 'difficulty' => 'High')
            );
    }

    // Return keywords
    wp_send_json_success(array('keywords' => $keywords));
}

// AJAX handler for generating blog post
add_action('wp_ajax_bpg_generate_post', 'bpg_generate_post');

function bpg_generate_post() {
    // Check nonce
    if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'bpg_nonce')) {
        wp_send_json_error(array('message' => 'Invalid nonce'));
    }

    // Check if keyword is provided
    if (!isset($_POST['keyword']) || empty($_POST['keyword'])) {
        wp_send_json_error(array('message' => 'Keyword is required'));
    }

    // Get parameters
    $keyword = sanitize_text_field($_POST['keyword']);
    $category = isset($_POST['category']) ? intval($_POST['category']) : 0;
    $post_status = isset($_POST['post_status']) ? sanitize_text_field($_POST['post_status']) : 'draft';

    // Simulate blog post generation with sample data
    $title = 'The Ultimate Guide to ' . ucwords($keyword);
    $content = '<h2>Introduction</h2>';
    $content .= '<p>Welcome to our comprehensive guide on ' . $keyword . '. In this article, we\'ll explore everything you need to know about this topic.</p>';
    $content .= '<h2>What is ' . ucwords($keyword) . '?</h2>';
    $content .= '<p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nullam auctor, nisl eget ultricies tincidunt, nisl nisl aliquam nisl, eget ultricies nisl nisl eget nisl.</p>';
    $content .= '<h2>Benefits of ' . ucwords($keyword) . '</h2>';
    $content .= '<ul>';
    $content .= '<li>Benefit 1: Lorem ipsum dolor sit amet</li>';
    $content .= '<li>Benefit 2: Consectetur adipiscing elit</li>';
    $content .= '<li>Benefit 3: Nullam auctor, nisl eget ultricies tincidunt</li>';
    $content .= '</ul>';
    $content .= '<h2>How to Get Started with ' . ucwords($keyword) . '</h2>';
    $content .= '<p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nullam auctor, nisl eget ultricies tincidunt, nisl nisl aliquam nisl, eget ultricies nisl nisl eget nisl.</p>';
    $content .= '<h3>Key Takeaways</h3>';
    $content .= '<p>Remember these important points about ' . $keyword . ':</p>';
    $content .= '<ul>';
    $content .= '<li>Key point 1: Lorem ipsum dolor sit amet</li>';
    $content .= '<li>Key point 2: Consectetur adipiscing elit</li>';
    $content .= '<li>Key point 3: Nullam auctor, nisl eget ultricies tincidunt</li>';
    $content .= '</ul>';
    $content .= '<h2>Conclusion</h2>';
    $content .= '<p>Now that you understand ' . $keyword . ', you can start implementing these strategies in your daily life.</p>';

    // Return generated content
    wp_send_json_success(array(
        'title' => $title,
        'content' => $content
    ));
}

// AJAX handler for saving blog post
add_action('wp_ajax_bpg_save_post', 'bpg_save_post');

function bpg_save_post() {
    // Check nonce
    if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'bpg_nonce')) {
        wp_send_json_error(array('message' => 'Invalid nonce'));
    }

    // Check if title and content are provided
    if (!isset($_POST['title']) || empty($_POST['title']) || !isset($_POST['content']) || empty($_POST['content'])) {
        wp_send_json_error(array('message' => 'Title and content are required'));
    }

    // Get parameters
    $title = sanitize_text_field($_POST['title']);
    $content = wp_kses_post($_POST['content']);
    $category = isset($_POST['category']) ? intval($_POST['category']) : 0;
    $post_status = isset($_POST['post_status']) ? sanitize_text_field($_POST['post_status']) : 'draft';

    // Create post
    $post_id = wp_insert_post(array(
        'post_title' => $title,
        'post_content' => $content,
        'post_status' => $post_status,
        'post_author' => get_current_user_id(),
        'post_category' => array($category)
    ));

    if (is_wp_error($post_id)) {
        wp_send_json_error(array('message' => $post_id->get_error_message()));
    }

    // Return success
    wp_send_json_success(array(
        'post_id' => $post_id,
        'edit_url' => get_edit_post_link($post_id, 'raw'),
        'view_url' => get_permalink($post_id)
    ));
}

// Create necessary directories on plugin activation
register_activation_hook(__FILE__, 'bpg_activate');

function bpg_activate() {
    // Create directories if they don't exist
    $dirs = array(
        BPG_PLUGIN_DIR . 'assets',
        BPG_PLUGIN_DIR . 'assets/css',
        BPG_PLUGIN_DIR . 'assets/js',
        BPG_PLUGIN_DIR . 'templates'
    );

    foreach ($dirs as $dir) {
        if (!file_exists($dir)) {
            wp_mkdir_p($dir);
        }
    }
}
