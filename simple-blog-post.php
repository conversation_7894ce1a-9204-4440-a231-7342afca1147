<?php
/**
 * Plugin Name: Simple Blog Post Generator
 * Plugin URI: https://example.com/simple-blog-post
 * Description: A simplified version of the blog post generator with tab navigation
 * Version: 1.0.0
 * Author: Augment Agent
 * Author URI: https://example.com
 * Text Domain: simple-blog-post
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('SBPG_VERSION', '1.0.0');
define('SBPG_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('SBPG_PLUGIN_URL', plugin_dir_url(__FILE__));

// Add admin menu
add_action('admin_menu', 'sbpg_add_menu');

function sbpg_add_menu() {
    add_menu_page(
        'Simple Blog Post Generator',
        'Blog Generator',
        'manage_options',
        'simple-blog-post',
        'sbpg_admin_page',
        'dashicons-edit',
        30
    );
}

// Enqueue scripts and styles
add_action('admin_enqueue_scripts', 'sbpg_enqueue_scripts');

function sbpg_enqueue_scripts($hook) {
    if ($hook != 'toplevel_page_simple-blog-post') {
        return;
    }
    
    // Enqueue jQuery UI
    wp_enqueue_script('jquery-ui-tabs');
    
    // Enqueue custom styles and scripts
    wp_enqueue_style('sbpg-admin-style', SBPG_PLUGIN_URL . 'assets/css/admin.css', array(), SBPG_VERSION);
    wp_enqueue_script('sbpg-admin-script', SBPG_PLUGIN_URL . 'assets/js/admin.js', array('jquery', 'jquery-ui-tabs'), SBPG_VERSION, true);
    
    // Localize script with data
    wp_localize_script('sbpg-admin-script', 'sbpg_params', array(
        'ajax_url' => admin_url('admin-ajax.php'),
        'nonce' => wp_create_nonce('sbpg_nonce'),
        'researching_text' => __('Researching...', 'simple-blog-post'),
        'generating_text' => __('Generating...', 'simple-blog-post'),
        'current_user' => wp_get_current_user()->display_name
    ));
}

// Admin page content
function sbpg_admin_page() {
    // Get categories
    $categories = get_categories(array(
        'hide_empty' => false,
        'orderby' => 'name',
        'order' => 'ASC'
    ));
    
    // Include admin template
    include_once SBPG_PLUGIN_DIR . 'templates/admin.php';
}

// AJAX handler for keyword research
add_action('wp_ajax_sbpg_research_keywords', 'sbpg_research_keywords');

function sbpg_research_keywords() {
    // Check nonce
    if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'sbpg_nonce')) {
        wp_send_json_error(array('message' => 'Invalid nonce'));
    }
    
    // Check if niche is provided
    if (!isset($_POST['niche']) || empty($_POST['niche'])) {
        wp_send_json_error(array('message' => 'Niche is required'));
    }
    
    // Get niche
    $niche = sanitize_text_field($_POST['niche']);
    
    // Simulate keyword research with sample data
    $keywords = array();
    
    switch ($niche) {
        case 'health-fitness':
            $keywords = array(
                array('keyword' => 'best home workouts', 'search_volume' => '12,000', 'difficulty' => 'Medium'),
                array('keyword' => 'weight loss tips', 'search_volume' => '22,000', 'difficulty' => 'High'),
                array('keyword' => 'healthy breakfast ideas', 'search_volume' => '8,500', 'difficulty' => 'Low'),
                array('keyword' => 'intermittent fasting benefits', 'search_volume' => '9,200', 'difficulty' => 'Medium')
            );
            break;
        case 'nutrition':
            $keywords = array(
                array('keyword' => 'protein-rich foods', 'search_volume' => '14,500', 'difficulty' => 'Medium'),
                array('keyword' => 'keto diet plan', 'search_volume' => '18,000', 'difficulty' => 'High'),
                array('keyword' => 'vegan protein sources', 'search_volume' => '7,800', 'difficulty' => 'Low'),
                array('keyword' => 'best vitamins for energy', 'search_volume' => '6,300', 'difficulty' => 'Medium')
            );
            break;
        default:
            $keywords = array(
                array('keyword' => 'sample keyword 1', 'search_volume' => '5,000', 'difficulty' => 'Low'),
                array('keyword' => 'sample keyword 2', 'search_volume' => '7,500', 'difficulty' => 'Medium'),
                array('keyword' => 'sample keyword 3', 'search_volume' => '10,000', 'difficulty' => 'High')
            );
    }
    
    // Return keywords
    wp_send_json_success(array('keywords' => $keywords));
}

// AJAX handler for generating blog post
add_action('wp_ajax_sbpg_generate_post', 'sbpg_generate_post');

function sbpg_generate_post() {
    // Check nonce
    if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'sbpg_nonce')) {
        wp_send_json_error(array('message' => 'Invalid nonce'));
    }
    
    // Check if keyword is provided
    if (!isset($_POST['keyword']) || empty($_POST['keyword'])) {
        wp_send_json_error(array('message' => 'Keyword is required'));
    }
    
    // Get parameters
    $keyword = sanitize_text_field($_POST['keyword']);
    $category = isset($_POST['category']) ? intval($_POST['category']) : 0;
    $post_status = isset($_POST['post_status']) ? sanitize_text_field($_POST['post_status']) : 'draft';
    
    // Simulate blog post generation with sample data
    $title = 'The Ultimate Guide to ' . ucwords($keyword);
    $content = '<h2>Introduction</h2>';
    $content .= '<p>Welcome to our comprehensive guide on ' . $keyword . '. In this article, we\'ll explore everything you need to know about this topic.</p>';
    $content .= '<h2>What is ' . ucwords($keyword) . '?</h2>';
    $content .= '<p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nullam auctor, nisl eget ultricies tincidunt, nisl nisl aliquam nisl, eget ultricies nisl nisl eget nisl.</p>';
    $content .= '<h2>Benefits of ' . ucwords($keyword) . '</h2>';
    $content .= '<ul>';
    $content .= '<li>Benefit 1: Lorem ipsum dolor sit amet</li>';
    $content .= '<li>Benefit 2: Consectetur adipiscing elit</li>';
    $content .= '<li>Benefit 3: Nullam auctor, nisl eget ultricies tincidunt</li>';
    $content .= '</ul>';
    $content .= '<h2>How to Get Started with ' . ucwords($keyword) . '</h2>';
    $content .= '<p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nullam auctor, nisl eget ultricies tincidunt, nisl nisl aliquam nisl, eget ultricies nisl nisl eget nisl.</p>';
    $content .= '<h3>Key Takeaways</h3>';
    $content .= '<p>Remember these important points about ' . $keyword . ':</p>';
    $content .= '<ul>';
    $content .= '<li>Key point 1: Lorem ipsum dolor sit amet</li>';
    $content .= '<li>Key point 2: Consectetur adipiscing elit</li>';
    $content .= '<li>Key point 3: Nullam auctor, nisl eget ultricies tincidunt</li>';
    $content .= '</ul>';
    $content .= '<h2>Conclusion</h2>';
    $content .= '<p>Now that you understand ' . $keyword . ', you can start implementing these strategies in your daily life.</p>';
    
    // Return generated content
    wp_send_json_success(array(
        'title' => $title,
        'content' => $content
    ));
}

// AJAX handler for saving blog post
add_action('wp_ajax_sbpg_save_post', 'sbpg_save_post');

function sbpg_save_post() {
    // Check nonce
    if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'sbpg_nonce')) {
        wp_send_json_error(array('message' => 'Invalid nonce'));
    }
    
    // Check if title and content are provided
    if (!isset($_POST['title']) || empty($_POST['title']) || !isset($_POST['content']) || empty($_POST['content'])) {
        wp_send_json_error(array('message' => 'Title and content are required'));
    }
    
    // Get parameters
    $title = sanitize_text_field($_POST['title']);
    $content = wp_kses_post($_POST['content']);
    $category = isset($_POST['category']) ? intval($_POST['category']) : 0;
    $post_status = isset($_POST['post_status']) ? sanitize_text_field($_POST['post_status']) : 'draft';
    
    // Create post
    $post_id = wp_insert_post(array(
        'post_title' => $title,
        'post_content' => $content,
        'post_status' => $post_status,
        'post_author' => get_current_user_id(),
        'post_category' => array($category)
    ));
    
    if (is_wp_error($post_id)) {
        wp_send_json_error(array('message' => $post_id->get_error_message()));
    }
    
    // Return success
    wp_send_json_success(array(
        'post_id' => $post_id,
        'edit_url' => get_edit_post_link($post_id, 'raw'),
        'view_url' => get_permalink($post_id)
    ));
}

// Create necessary directories on plugin activation
register_activation_hook(__FILE__, 'sbpg_activate');

function sbpg_activate() {
    // Create directories if they don't exist
    $dirs = array(
        SBPG_PLUGIN_DIR . 'assets',
        SBPG_PLUGIN_DIR . 'assets/css',
        SBPG_PLUGIN_DIR . 'assets/js',
        SBPG_PLUGIN_DIR . 'templates'
    );
    
    foreach ($dirs as $dir) {
        if (!file_exists($dir)) {
            wp_mkdir_p($dir);
        }
    }
}
