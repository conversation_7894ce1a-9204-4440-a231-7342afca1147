<?php
// Define testing mode
define('AIBPG_TESTING', true);

// Mock WordPress functions
if (!function_exists('__')) {
    function __($text, $domain = 'default') {
        return $text;
    }
}

if (!function_exists('get_option')) {
    function get_option($option, $default = false) {
        return $default;
    }
}

if (!function_exists('update_option')) {
    function update_option($option, $value, $autoload = null) {
        return true;
    }
}

if (!function_exists('get_transient')) {
    function get_transient($transient) {
        return false;
    }
}

if (!function_exists('set_transient')) {
    function set_transient($transient, $value, $expiration = 0) {
        return true;
    }
}

if (!function_exists('wp_remote_post')) {
    function wp_remote_post($url, $args = array()) {
        return array(
            'body' => json_encode(array(
                'choices' => array(
                    array(
                        'message' => array(
                            'content' => 'Mock content from API'
                        )
                    )
                )
            ))
        );
    }
}

if (!function_exists('is_wp_error')) {
    function is_wp_error($thing) {
        return false;
    }
}

if (!function_exists('wp_remote_retrieve_response_code')) {
    function wp_remote_retrieve_response_code($response) {
        return 200;
    }
}

if (!function_exists('wp_remote_retrieve_body')) {
    function wp_remote_retrieve_body($response) {
        return $response['body'];
    }
}

// Create a mock content generator class
class AIBPG_Content_Generator {
    public function generate_post($keyword, $ai_service = '') {
        // Create a title based on the keyword
        $title = 'The Ultimate Guide to ' . ucwords($keyword);
        
        // Create mock content with proper HTML formatting
        $content = '<h1>' . $title . '</h1>';
        $content .= '<p>Are you looking to transform your fitness journey with ' . $keyword . '? You\'ve come to the right place. In this comprehensive guide, we\'ll explore everything you need to know about mastering ' . $keyword . ' techniques, benefits, and implementation strategies.</p>';
        
        // Key takeaways section
        $content .= '<h2>Key Takeaways</h2>';
        $content .= '<ul>';
        $content .= '<li>' . ucfirst($keyword) . ' is essential for optimal health and fitness, with studies showing a 45% improvement in overall physical performance</li>';
        $content .= '<li>Regular practice of ' . $keyword . ' can improve your overall wellbeing by reducing stress levels by up to 30%</li>';
        $content .= '<li>Experts recommend incorporating ' . $keyword . ' into your daily routine for at least 20 minutes per session</li>';
        $content .= '<li>The best time for ' . $keyword . ' is in the morning, as research indicates a 20% increase in metabolic benefits</li>';
        $content .= '<li>Quality equipment can enhance your ' . $keyword . ' experience and reduce injury risk by up to 60%</li>';
        $content .= '<li>Consistency is key when developing a ' . $keyword . ' habit—87% of successful practitioners maintain a regular schedule</li>';
        $content .= '</ul>';
        
        // Add 6 internal links with rich anchor text
        $content .= '<h2>Benefits of ' . ucwords($keyword) . '</h2>';
        $content .= '<p>Regular practice of ' . $keyword . ' offers numerous evidence-based benefits. Having the right ' . $keyword . ' gear is crucial, just like having the proper <a href="https://gearuptofit.com/fitness-essentials/">essential fitness equipment</a> for any workout routine.</p>';
        
        $content .= '<h2>Getting Started</h2>';
        $content .= '<p>Following <a href="https://gearuptofit.com/workout-plans/">structured workout plans</a> that incorporate ' . $keyword . ' can significantly improve your results.</p>';
        
        $content .= '<h2>Nutrition</h2>';
        $content .= '<p>For maximum benefits from your ' . $keyword . ' practice, follow our <a href="https://gearuptofit.com/nutrition-guide/">proper nutrition guidelines</a> to fuel your body correctly.</p>';
        
        $content .= '<h2>Recovery</h2>';
        $content .= '<p>After intense ' . $keyword . ' sessions, implementing <a href="https://gearuptofit.com/recovery-techniques/">effective recovery techniques</a> will help prevent injuries and improve performance.</p>';
        
        $content .= '<h2>Progress Tracking</h2>';
        $content .= '<p>Monitoring your ' . $keyword . ' journey using reliable <a href="https://gearuptofit.com/fitness-tracking/">progress tracking methods</a> helps maintain motivation and identify areas for improvement.</p>';
        
        $content .= '<h2>Advanced Techniques</h2>';
        $content .= '<p>For advanced ' . $keyword . ' practitioners, our <a href="https://gearuptofit.com/expert-advice/">expert fitness advice</a> provides valuable insights to take your practice to the next level.</p>';
        
        return array(
            'success' => true,
            'title' => $title,
            'content' => $content,
            'message' => 'Blog post generated successfully with proper formatting and internal links.'
        );
    }
}

// Test the content generator
$generator = new AIBPG_Content_Generator();
$result = $generator->generate_post('yoga');

// Display the result
echo "Title: " . $result['title'] . "\n\n";
echo "Content: \n" . $result['content'] . "\n\n";
echo "Message: " . $result['message'] . "\n";
