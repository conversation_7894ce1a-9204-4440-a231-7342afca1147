<?php
/**
 * API class for AI Blog Post Generator
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

class AIBPG_API {
    /**
     * Available models for each service
     */
    private $available_models = array(
        'openai' => array(
            'gpt-4o' => 'GPT-4o',
            'gpt-4-turbo' => 'GPT-4 Turbo',
            'gpt-4' => 'GPT-4',
            'gpt-3.5-turbo' => 'GPT-3.5 Turbo',
        ),
        'claude' => array(
            'claude-3-opus-20240229' => 'Claude 3 Opus',
            'claude-3-sonnet-20240229' => 'Claude 3 Sonnet',
            'claude-3-haiku-********' => 'Claude 3 Haiku',
            'claude-2.1' => 'Claude 2.1',
            'claude-2.0' => 'Claude 2.0',
        ),
        'gemini' => array(
            'gemini-1.5-pro' => 'Gemini 1.5 Pro',
            'gemini-1.5-flash' => 'Gemini 1.5 Flash',
            'gemini-pro' => 'Gemini Pro',
            'gemini-pro-vision' => 'Gemini Pro Vision',
        ),
        'openrouter' => array(
            'anthropic/claude-3-opus' => 'Claude 3 Opus (via OpenRouter)',
            'anthropic/claude-3-sonnet' => 'Claude 3 Sonnet (via OpenRouter)',
            'anthropic/claude-3-haiku' => 'Claude 3 Haiku (via OpenRouter)',
            'openai/gpt-4o' => 'GPT-4o (via OpenRouter)',
            'openai/gpt-4-turbo' => 'GPT-4 Turbo (via OpenRouter)',
            'google/gemini-1.5-pro' => 'Gemini 1.5 Pro (via OpenRouter)',
            'meta-llama/llama-3-70b-instruct' => 'Llama 3 70B (via OpenRouter)',
        ),
    );

    /**
     * API validation rate limiting
     */
    private $rate_limit = array(
        'max_attempts' => 5,      // Maximum validation attempts
        'timeframe' => 3600,      // Timeframe in seconds (1 hour)
        'lockout_time' => 7200    // Lockout time in seconds (2 hours)
    );

    /**
     * Constructor
     */
    public function __construct() {
        // Register settings for model selection
        add_action('admin_init', array($this, 'register_model_settings'));

        // Initialize encryption if not already set up
        $this->maybe_initialize_encryption();
    }

    /**
     * Register settings for model selection
     */
    public function register_model_settings() {
        // Register settings for preferred models
        register_setting('aibpg_api_settings', 'aibpg_openai_preferred_model');
        register_setting('aibpg_api_settings', 'aibpg_claude_preferred_model');
        register_setting('aibpg_api_settings', 'aibpg_gemini_preferred_model');
        register_setting('aibpg_api_settings', 'aibpg_openrouter_preferred_model');
    }

    /**
     * Initialize encryption for API keys if not already set up
     */
    private function maybe_initialize_encryption() {
        // Check if encryption key is set
        if (!get_option('aibpg_encryption_key')) {
            // Generate a secure random key
            $encryption_key = bin2hex(openssl_random_pseudo_bytes(32));
            update_option('aibpg_encryption_key', $encryption_key);
        }

        // Check if we need to migrate existing API keys to encrypted storage
        $this->maybe_migrate_api_keys();
    }

    /**
     * Migrate existing API keys to encrypted storage
     */
    private function maybe_migrate_api_keys() {
        $services = array('openai', 'claude', 'gemini', 'openrouter');
        $migrated = false;

        foreach ($services as $service) {
            $option_name = 'aibpg_' . $service . '_api_key';
            $encrypted_option_name = 'aibpg_' . $service . '_api_key_encrypted';

            // Check if we have an unencrypted key but no encrypted key
            $api_key = get_option($option_name, '');
            $encrypted_key = get_option($encrypted_option_name, '');

            if (!empty($api_key) && empty($encrypted_key)) {
                // Encrypt and store the key
                $encrypted_key = $this->encrypt_api_key($api_key);
                update_option($encrypted_option_name, $encrypted_key);

                // Store a masked version for display
                $masked_key = $this->mask_api_key($api_key);
                update_option($option_name, $masked_key);

                $migrated = true;
            }
        }

        if ($migrated) {
            // Set a flag indicating migration has occurred
            update_option('aibpg_api_keys_migrated', 'yes');
        }
    }

    /**
     * Encrypt an API key
     *
     * @param string $api_key The API key to encrypt
     * @return string Encrypted API key
     */
    public function encrypt_api_key($api_key) {
        if (empty($api_key)) {
            return '';
        }

        $encryption_key = get_option('aibpg_encryption_key', '');

        if (empty($encryption_key)) {
            // If no encryption key, initialize it
            $this->maybe_initialize_encryption();
            $encryption_key = get_option('aibpg_encryption_key', '');
        }

        // Create a random initialization vector
        $iv = openssl_random_pseudo_bytes(openssl_cipher_iv_length('aes-256-cbc'));

        // Encrypt the API key
        $encrypted = openssl_encrypt($api_key, 'aes-256-cbc', $encryption_key, 0, $iv);

        // Combine the IV and encrypted data
        $encrypted_with_iv = base64_encode($iv . $encrypted);

        return $encrypted_with_iv;
    }

    /**
     * Decrypt an API key
     *
     * @param string $encrypted_api_key The encrypted API key
     * @return string Decrypted API key
     */
    public function decrypt_api_key($encrypted_api_key) {
        if (empty($encrypted_api_key)) {
            return '';
        }

        $encryption_key = get_option('aibpg_encryption_key', '');

        if (empty($encryption_key)) {
            return '';
        }

        // Decode the combined IV and encrypted data
        $decoded = base64_decode($encrypted_api_key);

        // Extract the IV
        $iv_length = openssl_cipher_iv_length('aes-256-cbc');
        $iv = substr($decoded, 0, $iv_length);
        $encrypted = substr($decoded, $iv_length);

        // Decrypt the API key
        $decrypted = openssl_decrypt($encrypted, 'aes-256-cbc', $encryption_key, 0, $iv);

        return $decrypted;
    }

    /**
     * Mask an API key for display
     *
     * @param string $api_key The API key to mask
     * @return string Masked API key
     */
    public function mask_api_key($api_key) {
        if (empty($api_key)) {
            return '';
        }

        $length = strlen($api_key);

        if ($length <= 8) {
            return '********';
        }

        // Show first 4 and last 4 characters
        $masked = substr($api_key, 0, 4) . str_repeat('*', $length - 8) . substr($api_key, -4);

        return $masked;
    }

    /**
     * Get API key (decrypted)
     *
     * @param string $service The AI service
     * @return string Decrypted API key
     */
    public function get_api_key($service) {
        // For testing purposes, return a default API key
        if (defined('AIBPG_TESTING') && AIBPG_TESTING) {
            // Return test API keys for different services
            $test_keys = array(
                'openai' => 'sk-test-openai-key',
                'claude' => 'sk-test-claude-key',
                'gemini' => 'AIzaSyTestGeminiKey',
                'openrouter' => 'sk-test-openrouter-key'
            );

            if (isset($test_keys[$service])) {
                return $test_keys[$service];
            }
        }

        // Normal operation
        $encrypted_option_name = 'aibpg_' . $service . '_api_key_encrypted';
        $encrypted_key = get_option($encrypted_option_name, '');

        if (!empty($encrypted_key)) {
            return $this->decrypt_api_key($encrypted_key);
        }

        // Fallback to unencrypted key (for backward compatibility)
        $option_name = 'aibpg_' . $service . '_api_key';
        $api_key = get_option($option_name, '');

        // Check if this is a masked key
        if (!empty($api_key) && strpos($api_key, '****') !== false) {
            return '';
        }

        return $api_key;
    }

    /**
     * Save API key (encrypted)
     *
     * @param string $service The AI service
     * @param string $api_key The API key to save
     * @return bool Success status
     */
    public function save_api_key($service, $api_key) {
        if (empty($service)) {
            return false;
        }

        // Encrypt the API key
        $encrypted_key = $this->encrypt_api_key($api_key);
        $encrypted_option_name = 'aibpg_' . $service . '_api_key_encrypted';

        // Store the encrypted key
        $result = update_option($encrypted_option_name, $encrypted_key);

        // Store a masked version for display
        $masked_key = $this->mask_api_key($api_key);
        $option_name = 'aibpg_' . $service . '_api_key';
        update_option($option_name, $masked_key);

        return $result;
    }

    /**
     * Get available models for a service
     *
     * @param string $service The AI service
     * @param bool $refresh Whether to refresh the models list from the API
     * @return array Available models
     */
    public function get_available_models($service, $refresh = false) {
        // If we're not refreshing and we have models cached, return them
        if (!$refresh && isset($this->available_models[$service])) {
            return $this->available_models[$service];
        }

        // Special handling for Gemini models
        if ($service === 'gemini') {
            // Get stored models or use defaults
            $gemini_models = get_option('aibpg_gemini_available_models', array());

            if (empty($gemini_models)) {
                $gemini_models = array(
                    'gemini-pro' => 'Gemini Pro',
                    'gemini-pro-vision' => 'Gemini Pro Vision',
                    'gemini-1.5-pro' => 'Gemini 1.5 Pro',
                    'gemini-1.5-flash' => 'Gemini 1.5 Flash'
                );
            }

            return $gemini_models;
        }

        // If we need to refresh or don't have models cached, try to get them from the API
        if ($service === 'openrouter') {
            $api_key = $this->get_api_key($service);
            if (!empty($api_key)) {
                $models = $this->get_openrouter_models($api_key);
                if (!empty($models)) {
                    return $models;
                }
            }
        }

        // Fall back to predefined models if we couldn't get them from the API
        if (isset($this->available_models[$service])) {
            return $this->available_models[$service];
        }

        return array();
    }

    /**
     * Get all available models across all services
     *
     * @return array All available models grouped by service
     */
    public function get_all_available_models() {
        return $this->available_models;
    }

    /**
     * Get detected models for a service
     *
     * @param string $service The AI service
     * @param string $api_key The API key
     * @return array Detected models
     */
    public function get_detected_models($service, $api_key) {
        switch ($service) {
            case 'openai':
                return $this->get_openai_models($api_key);
            case 'claude':
                return $this->get_claude_models($api_key);
            case 'gemini':
                return $this->get_gemini_models($api_key);
            case 'openrouter':
                return $this->get_openrouter_models($api_key);
            default:
                return array();
        }
    }

    /**
     * Validate API key
     *
     * @param string $service The AI service (openai, claude, gemini, openrouter)
     * @param string $api_key The API key to validate
     * @param string $model Optional model to validate with
     * @return array Result with valid status, message, and available models
     */
    public function validate_api_key($service, $api_key, $model = '') {
        // Check if API key is empty
        if (empty($api_key)) {
            return array(
                'valid' => false,
                'message' => __('API key cannot be empty.', 'ai-blog-post-generator'),
                'models' => array()
            );
        }

        // Check if API key format is valid
        if (!$this->is_valid_api_key_format($service, $api_key)) {
            return array(
                'valid' => false,
                'message' => __('API key format is invalid.', 'ai-blog-post-generator'),
                'models' => array()
            );
        }

        // Validate with service-specific method
        try {
            $result = array('valid' => false, 'message' => __('Unknown error occurred.', 'ai-blog-post-generator'));

            switch ($service) {
                case 'openai':
                    $result = $this->validate_openai_key($api_key, $model);
                    break;
                case 'claude':
                    $result = $this->validate_claude_key($api_key, $model);
                    break;
                case 'gemini':
                    $result = $this->validate_gemini_key($api_key, $model);
                    break;
                case 'openrouter':
                    $result = $this->validate_openrouter_key($api_key, $model);
                    break;
                default:
                    return array(
                        'valid' => false,
                        'message' => __('Invalid service.', 'ai-blog-post-generator'),
                        'models' => array()
                    );
            }

            // Log validation attempt
            $this->log_validation_attempt($service, $result['valid']);

            return $result;
        } catch (Exception $e) {
            // Log validation error
            $this->log_validation_attempt($service, false);

            return array(
                'valid' => false,
                'message' => sprintf(__('Error validating API key: %s', 'ai-blog-post-generator'), $e->getMessage()),
                'models' => array()
            );
        }
    }

    /**
     * Check if API key format is valid
     *
     * @param string $service The AI service
     * @param string $api_key The API key to validate
     * @return bool Whether the API key format is valid
     */
    private function is_valid_api_key_format($service, $api_key) {
        // Trim the API key to remove any whitespace
        $api_key = trim($api_key);

        // Log validation attempt (without showing the full key)
        $masked_key = substr($api_key, 0, 4) . '...' . substr($api_key, -4);
        error_log(sprintf('Validating API key format for service: %s, key: %s', $service, $masked_key));

        // Basic validation - check if key is empty or too short
        if (empty($api_key) || strlen($api_key) < 8) {
            error_log('API key validation failed: Key too short or empty');
            return false;
        }

        // Check for common issues like spaces or newlines
        if (preg_match('/\s/', $api_key)) {
            error_log('API key validation failed: Key contains whitespace');
            return false;
        }

        // Define regex patterns for each service
        $patterns = array(
            'openai' => '/^sk-[a-zA-Z0-9]{32,}$/',
            'claude' => '/^sk-ant-api[0-9]{2}-[a-zA-Z0-9-_]{48,}$/',
            'gemini' => '/^[a-zA-Z0-9_-]{39}$/',
            'openrouter' => '/^sk-or-[a-zA-Z0-9-]{24,}$/'
        );

        // If service doesn't have a pattern, do basic validation
        if (!isset($patterns[$service])) {
            // Basic validation - at least 20 characters and no spaces
            return strlen($api_key) >= 20 && !preg_match('/\s/', $api_key);
        }

        // Check if API key matches the pattern
        $matches = preg_match($patterns[$service], $api_key);

        // Service-specific additional checks
        switch ($service) {
            case 'openai':
                // OpenAI keys must start with 'sk-'
                if (strpos($api_key, 'sk-') !== 0) {
                    error_log('OpenAI API key validation failed: Key does not start with sk-');
                    return false;
                }
                break;

            case 'claude':
                // Claude keys must start with 'sk-ant-api'
                if (strpos($api_key, 'sk-ant-api') !== 0) {
                    error_log('Claude API key validation failed: Key does not start with sk-ant-api');
                    return false;
                }
                break;

            case 'openrouter':
                // For OpenRouter, also accept keys that start with 'sk-' as they may have different formats
                if ($matches !== 1) {
                    return strpos($api_key, 'sk-') === 0 && strlen($api_key) >= 20;
                }
                break;
        }

        return $matches === 1;
    }

    /**
     * Log validation attempt
     *
     * @param string $service The AI service
     * @param bool $success Whether the validation was successful
     */
    private function log_validation_attempt($service, $success) {
        $validations = get_option('aibpg_api_validations', array());

        if (!isset($validations[$service])) {
            $validations[$service] = array(
                'total' => 0,
                'success' => 0,
                'failure' => 0,
                'last_attempt' => 0
            );
        }

        $validations[$service]['total']++;

        if ($success) {
            $validations[$service]['success']++;
        } else {
            $validations[$service]['failure']++;
        }

        $validations[$service]['last_attempt'] = time();

        update_option('aibpg_api_validations', $validations);
    }

    /**
     * Get OpenAI models
     *
     * @param string $api_key The API key
     * @return array Available models
     */
    private function get_openai_models($api_key) {
        $response = wp_remote_get('https://api.openai.com/v1/models', array(
            'headers' => array(
                'Authorization' => 'Bearer ' . $api_key,
                'Content-Type' => 'application/json'
            ),
            'timeout' => 15
        ));

        if (is_wp_error($response) || wp_remote_retrieve_response_code($response) !== 200) {
            return array();
        }

        $response_body = json_decode(wp_remote_retrieve_body($response), true);
        $available_models = array();

        if (isset($response_body['data']) && is_array($response_body['data'])) {
            foreach ($response_body['data'] as $model) {
                if (isset($model['id'])) {
                    $model_id = $model['id'];

                    // Filter for chat models
                    if (strpos($model_id, 'gpt-4') === 0 || strpos($model_id, 'gpt-3.5-turbo') === 0) {
                        $available_models[$model_id] = $model_id;
                    }
                }
            }
        }

        return $available_models;
    }

    /**
     * Validate OpenAI API key
     *
     * @param string $api_key The API key to validate
     * @param string $model Optional model to validate with
     * @return array Result with valid status, message, and available models
     */
    private function validate_openai_key($api_key, $model = '') {
        // First, get available models
        $models_response = wp_remote_get('https://api.openai.com/v1/models', array(
            'headers' => array(
                'Authorization' => 'Bearer ' . $api_key,
                'Content-Type' => 'application/json'
            ),
            'timeout' => 15
        ));

        if (is_wp_error($models_response)) {
            return array(
                'valid' => false,
                'message' => $models_response->get_error_message(),
                'models' => array()
            );
        }

        $response_code = wp_remote_retrieve_response_code($models_response);
        $response_body = json_decode(wp_remote_retrieve_body($models_response), true);

        if ($response_code === 200) {
            // Extract available models
            $available_models = array();

            if (isset($response_body['data']) && is_array($response_body['data'])) {
                foreach ($response_body['data'] as $model_data) {
                    if (isset($model_data['id'])) {
                        $model_id = $model_data['id'];

                        // Filter for chat models
                        if (strpos($model_id, 'gpt-4') === 0 || strpos($model_id, 'gpt-3.5-turbo') === 0) {
                            $available_models[$model_id] = $model_id;
                        }
                    }
                }
            }

            // If a specific model was provided, test it
            if (!empty($model) && isset($available_models[$model])) {
                $test_response = wp_remote_post('https://api.openai.com/v1/chat/completions', array(
                    'headers' => array(
                        'Authorization' => 'Bearer ' . $api_key,
                        'Content-Type' => 'application/json'
                    ),
                    'body' => json_encode(array(
                        'model' => $model,
                        'messages' => array(
                            array(
                                'role' => 'user',
                                'content' => 'Say hello'
                            )
                        ),
                        'max_tokens' => 10
                    )),
                    'timeout' => 15
                ));

                if (is_wp_error($test_response) || wp_remote_retrieve_response_code($test_response) !== 200) {
                    $error_message = is_wp_error($test_response)
                        ? $test_response->get_error_message()
                        : json_decode(wp_remote_retrieve_body($test_response), true)['error']['message'] ?? __('Failed to use the specified model.', 'ai-blog-post-generator');

                    return array(
                        'valid' => true,
                        'message' => sprintf(__('API key is valid, but there was an issue with the %s model: %s', 'ai-blog-post-generator'), $model, $error_message),
                        'models' => $available_models
                    );
                }

                return array(
                    'valid' => true,
                    'message' => sprintf(__('OpenAI API key is valid and %s model is working properly.', 'ai-blog-post-generator'), $model),
                    'models' => $available_models
                );
            }

            return array(
                'valid' => true,
                'message' => __('OpenAI API key is valid.', 'ai-blog-post-generator'),
                'models' => $available_models
            );
        } else {
            $error_message = isset($response_body['error']['message'])
                ? $response_body['error']['message']
                : __('Invalid API key.', 'ai-blog-post-generator');

            return array(
                'valid' => false,
                'message' => $error_message,
                'models' => array()
            );
        }
    }

    /**
     * Get Claude models
     *
     * @param string $api_key The API key
     * @return array Available models
     */
    private function get_claude_models($api_key) {
        // Claude doesn't have a models endpoint, so we'll return our predefined list
        // and check if they're available by testing the key
        $models = array(
            'claude-3-opus-20240229' => 'Claude 3 Opus',
            'claude-3-sonnet-20240229' => 'Claude 3 Sonnet',
            'claude-3-haiku-********' => 'Claude 3 Haiku',
            'claude-2.1' => 'Claude 2.1',
            'claude-2.0' => 'Claude 2.0'
        );

        // Test the API key with a simple request
        $response = wp_remote_post('https://api.anthropic.com/v1/messages', array(
            'headers' => array(
                'x-api-key' => $api_key,
                'anthropic-version' => '2023-06-01',
                'Content-Type' => 'application/json'
            ),
            'body' => json_encode(array(
                'model' => 'claude-3-haiku-********', // Use the smallest model for testing
                'max_tokens' => 10,
                'messages' => array(
                    array(
                        'role' => 'user',
                        'content' => 'Say hello'
                    )
                )
            )),
            'timeout' => 15
        ));

        if (is_wp_error($response) || wp_remote_retrieve_response_code($response) !== 200) {
            return array();
        }

        return $models;
    }

    /**
     * Validate Claude API key
     *
     * @param string $api_key The API key to validate
     * @param string $model Optional model to validate with
     * @return array Result with valid status, message, and available models
     */
    private function validate_claude_key($api_key, $model = '') {
        // Define available models
        $available_models = array(
            'claude-3-opus-20240229' => 'Claude 3 Opus',
            'claude-3-sonnet-20240229' => 'Claude 3 Sonnet',
            'claude-3-haiku-********' => 'Claude 3 Haiku',
            'claude-2.1' => 'Claude 2.1',
            'claude-2.0' => 'Claude 2.0'
        );

        // If a specific model was provided, use it for validation
        $test_model = !empty($model) && isset($available_models[$model])
            ? $model
            : 'claude-3-haiku-********'; // Default to the smallest model

        $response = wp_remote_post('https://api.anthropic.com/v1/messages', array(
            'headers' => array(
                'x-api-key' => $api_key,
                'anthropic-version' => '2023-06-01',
                'Content-Type' => 'application/json'
            ),
            'body' => json_encode(array(
                'model' => $test_model,
                'max_tokens' => 10,
                'messages' => array(
                    array(
                        'role' => 'user',
                        'content' => 'Say hello'
                    )
                )
            )),
            'timeout' => 15
        ));

        if (is_wp_error($response)) {
            return array(
                'valid' => false,
                'message' => $response->get_error_message(),
                'models' => array()
            );
        }

        $response_code = wp_remote_retrieve_response_code($response);
        $response_body = json_decode(wp_remote_retrieve_body($response), true);

        if ($response_code === 200 || $response_code === 201) {
            // If we're testing a specific model and it worked
            if (!empty($model) && $test_model === $model) {
                return array(
                    'valid' => true,
                    'message' => sprintf(__('Claude API key is valid and %s model is working properly.', 'ai-blog-post-generator'), $available_models[$model]),
                    'models' => $available_models
                );
            }

            return array(
                'valid' => true,
                'message' => __('Claude API key is valid.', 'ai-blog-post-generator'),
                'models' => $available_models
            );
        } else {
            // Check if it's a model-specific error
            if (isset($response_body['error']['type']) && $response_body['error']['type'] === 'invalid_model') {
                // The API key might be valid, but the model is not available
                // Try with a different model
                if ($test_model !== 'claude-3-haiku-********' && !empty($model)) {
                    $fallback_response = $this->validate_claude_key($api_key, '');
                    if ($fallback_response['valid']) {
                        return array(
                            'valid' => true,
                            'message' => sprintf(__('Claude API key is valid, but %s model is not available for your account.', 'ai-blog-post-generator'), $available_models[$model]),
                            'models' => $fallback_response['models']
                        );
                    }
                }
            }

            $error_message = isset($response_body['error']['message'])
                ? $response_body['error']['message']
                : __('Invalid API key.', 'ai-blog-post-generator');

            return array(
                'valid' => false,
                'message' => $error_message,
                'models' => array()
            );
        }
    }

    /**
     * Get Gemini models
     *
     * @param string $api_key The API key
     * @return array Available models
     */
    private function get_gemini_models($api_key) {
        $models = array();

        // Try to list models to check if the API key is valid
        $models_url = 'https://generativelanguage.googleapis.com/v1/models?key=' . $api_key;
        $response = wp_remote_get($models_url, array(
            'timeout' => 15
        ));

        if (is_wp_error($response) || wp_remote_retrieve_response_code($response) !== 200) {
            return $models;
        }

        $response_body = json_decode(wp_remote_retrieve_body($response), true);

        // Extract available models
        if (isset($response_body['models']) && is_array($response_body['models'])) {
            foreach ($response_body['models'] as $model) {
                if (isset($model['name'])) {
                    $model_name = basename($model['name']);

                    // Filter for Gemini models
                    if (strpos($model_name, 'gemini-') === 0) {
                        $display_name = ucwords(str_replace('-', ' ', $model_name));
                        $models[$model_name] = $display_name;
                    }
                }
            }
        }

        // If no models were found, add default ones
        if (empty($models)) {
            $models = array(
                'gemini-1.5-pro' => 'Gemini 1.5 Pro',
                'gemini-1.5-flash' => 'Gemini 1.5 Flash',
                'gemini-1.0-pro' => 'Gemini 1.0 Pro',
                'gemini-1.0-pro-vision' => 'Gemini 1.0 Pro Vision',
            );
        }

        // Save the models for future use
        update_option('aibpg_gemini_available_models', $models);

        return $models;
    }

    /**
     * Validate Gemini API key
     *
     * @param string $api_key The API key to validate
     * @param string $model Optional model to validate with
     * @return array Result with valid status, message, and available models
     */
    private function validate_gemini_key($api_key, $model = '') {
        // First, try to list models to check if the API key is valid
        $models_url = 'https://generativelanguage.googleapis.com/v1/models?key=' . $api_key;
        $response = wp_remote_get($models_url, array(
            'timeout' => 15
        ));

        if (is_wp_error($response)) {
            return array(
                'valid' => false,
                'message' => $response->get_error_message(),
                'models' => array()
            );
        }

        $response_code = wp_remote_retrieve_response_code($response);
        $response_body = json_decode(wp_remote_retrieve_body($response), true);

        // If we can list models, the API key is valid
        if ($response_code === 200) {
            // Extract available models
            $available_models = array();

            if (isset($response_body['models']) && is_array($response_body['models'])) {
                foreach ($response_body['models'] as $model_data) {
                    if (isset($model_data['name'])) {
                        $model_name = basename($model_data['name']);

                        // Filter for Gemini models
                        if (strpos($model_name, 'gemini-') === 0) {
                            $display_name = ucwords(str_replace('-', ' ', $model_name));
                            $available_models[$model_name] = $display_name;
                        }
                    }
                }
            }

            // If no models were found, add default ones
            if (empty($available_models)) {
                $available_models = array(
                    'gemini-1.5-pro' => 'Gemini 1.5 Pro',
                    'gemini-1.5-flash' => 'Gemini 1.5 Flash',
                    'gemini-pro' => 'Gemini Pro',
                    'gemini-pro-vision' => 'Gemini Pro Vision',
                );
            }

            // Store the available models for later use
            update_option('aibpg_gemini_available_models', $available_models);

            // Get the preferred model from settings or use default
            $preferred_model = get_option('aibpg_gemini_preferred_model', '');

            // If no preferred model is set, set it to gemini-pro
            if (empty($preferred_model) || !isset($available_models[$preferred_model])) {
                if (isset($available_models['gemini-pro'])) {
                    $preferred_model = 'gemini-pro';
                } else {
                    // Use the first available model
                    reset($available_models);
                    $preferred_model = key($available_models);
                }
                update_option('aibpg_gemini_preferred_model', $preferred_model);
            }

            // If a specific model was provided, test it
            if (!empty($model) && isset($available_models[$model])) {
                // Test the model
                $test_result = $this->test_gemini_model($api_key, $model);

                if ($test_result['success']) {
                    return array(
                        'valid' => true,
                        'message' => sprintf(__('Gemini API key is valid and %s model is working properly.', 'ai-blog-post-generator'), $available_models[$model]),
                        'models' => $available_models
                    );
                }

                // Model-specific error
                return array(
                    'valid' => true,
                    'message' => sprintf(__('Gemini API key is valid, but there was an issue with the %s model: %s', 'ai-blog-post-generator'),
                        $available_models[$model],
                        $test_result['message']),
                    'models' => $available_models
                );
            }

            // Test with preferred model
            $test_preferred = $this->test_gemini_model($api_key, $preferred_model);

            if ($test_preferred['success']) {
                return array(
                    'valid' => true,
                    'message' => sprintf(__('Gemini API key is valid and %s model is working properly.', 'ai-blog-post-generator'), $available_models[$preferred_model]),
                    'models' => $available_models
                );
            }

            // Try with gemini-pro if that's not what we just tried
            if ($preferred_model !== 'gemini-pro' && isset($available_models['gemini-pro'])) {
                $test_gemini_pro = $this->test_gemini_model($api_key, 'gemini-pro');

                if ($test_gemini_pro['success']) {
                    // Update the preferred model to gemini-pro since it works
                    update_option('aibpg_gemini_preferred_model', 'gemini-pro');

                    return array(
                        'valid' => true,
                        'message' => __('Gemini API key is valid and working with gemini-pro model.', 'ai-blog-post-generator'),
                        'models' => $available_models
                    );
                }
            }

            // If we couldn't generate content but the API key is valid
            return array(
                'valid' => true,
                'message' => __('Gemini API key is valid, but there might be issues with content generation.', 'ai-blog-post-generator'),
                'models' => $available_models
            );
        } else {
            $error_message = isset($response_body['error']['message'])
                ? $response_body['error']['message']
                : __('Invalid API key.', 'ai-blog-post-generator');

            return array(
                'valid' => false,
                'message' => $error_message,
                'models' => array()
            );
        }
    }

    /**
     * Test a specific Gemini model
     *
     * @param string $api_key The API key
     * @param string $model The model to test
     * @return array Result with success status and message
     */
    private function test_gemini_model($api_key, $model) {
        // Log the test attempt
        error_log("Testing Gemini model: {$model}");

        // Build the API URL
        $api_url = "https://generativelanguage.googleapis.com/v1/models/{$model}:generateContent?key=" . $api_key;

        // Build a simple request body
        $request_body = array(
            'contents' => array(
                array(
                    'role' => 'user',
                    'parts' => array(
                        array(
                            'text' => 'Say hello in one word'
                        )
                    )
                )
            ),
            'generationConfig' => array(
                'temperature' => 0.1,
                'maxOutputTokens' => 10
            )
        );

        // Make the API request
        $response = wp_remote_post($api_url, array(
            'headers' => array(
                'Content-Type' => 'application/json'
            ),
            'body' => json_encode($request_body),
            'timeout' => 15
        ));

        // Handle errors
        if (is_wp_error($response)) {
            $error_message = $response->get_error_message();
            error_log("Gemini model test error: {$error_message}");
            return array(
                'success' => false,
                'message' => $error_message
            );
        }

        // Process the response
        $response_code = wp_remote_retrieve_response_code($response);
        $response_body = json_decode(wp_remote_retrieve_body($response), true);

        // Log the response code
        error_log("Gemini model test response code: {$response_code}");

        // If there's an error, log it
        if ($response_code !== 200) {
            if (isset($response_body['error'])) {
                $error_message = isset($response_body['error']['message'])
                    ? $response_body['error']['message']
                    : 'Unknown error occurred';
                error_log("Gemini model test error: {$error_message}");
                error_log("Gemini model test error details: " . json_encode($response_body['error']));

                // Check if it's a model not found error
                if (strpos($error_message, 'not found for API version') !== false) {
                    return array(
                        'success' => false,
                        'message' => "Model {$model} not found. Please try a different model."
                    );
                }

                return array(
                    'success' => false,
                    'message' => $error_message
                );
            }
        }

        // Check if we have a valid response
        if ($response_code === 200 && isset($response_body['candidates'][0]['content']['parts'][0]['text'])) {
            return array(
                'success' => true,
                'message' => "Model {$model} is working properly."
            );
        } else {
            $error_message = isset($response_body['error']['message'])
                ? $response_body['error']['message']
                : "Failed to generate content with model {$model}.";

            return array(
                'success' => false,
                'message' => $error_message
            );
        }
    }

    /**
     * Get OpenRouter models
     *
     * @param string $api_key The API key
     * @return array Available models
     */
    private function get_openrouter_models($api_key) {
        $models = array();
        $cached_models = get_transient('aibpg_openrouter_models');

        // Return cached models if available (cache for 24 hours)
        if ($cached_models !== false) {
            return $cached_models;
        }

        // Get models from OpenRouter
        $response = wp_remote_get('https://openrouter.ai/api/v1/models', array(
            'headers' => array(
                'Authorization' => 'Bearer ' . $api_key,
                'Content-Type' => 'application/json',
                'HTTP-Referer' => site_url()
            ),
            'timeout' => 15
        ));

        if (is_wp_error($response)) {
            // Log the error for debugging
            error_log('OpenRouter API Error: ' . $response->get_error_message());
            // If we can't get models, return our predefined list
            return $this->available_models['openrouter'];
        }

        $response_code = wp_remote_retrieve_response_code($response);
        if ($response_code !== 200) {
            // Log the error for debugging
            error_log('OpenRouter API Error: HTTP ' . $response_code);
            // If we can't get models, return our predefined list
            return $this->available_models['openrouter'];
        }

        $response_body = json_decode(wp_remote_retrieve_body($response), true);

        if (isset($response_body['data']) && is_array($response_body['data'])) {
            // Sort models by provider and name
            usort($response_body['data'], function($a, $b) {
                // First sort by provider
                $provider_a = isset($a['id']) ? explode('/', $a['id'])[0] : '';
                $provider_b = isset($b['id']) ? explode('/', $b['id'])[0] : '';

                if ($provider_a !== $provider_b) {
                    return strcmp($provider_a, $provider_b);
                }

                // Then sort by name
                $name_a = isset($a['name']) ? $a['name'] : '';
                $name_b = isset($b['name']) ? $b['name'] : '';
                return strcmp($name_a, $name_b);
            });

            // Group models by provider
            $grouped_models = array();
            foreach ($response_body['data'] as $model_data) {
                if (isset($model_data['id'])) {
                    $model_id = $model_data['id'];
                    $model_name = isset($model_data['name']) ? $model_data['name'] : $model_id;

                    // Add context pricing if available
                    if (isset($model_data['context_length']) && isset($model_data['pricing']['prompt'])) {
                        $context_length = number_format($model_data['context_length']);
                        $prompt_price = $model_data['pricing']['prompt'];
                        $model_name .= " ({$context_length} tokens, \${$prompt_price}/1M tokens)";
                    }

                    $models[$model_id] = $model_name;
                }
            }
        }

        // If no models were found, use our predefined list
        if (empty($models)) {
            $models = $this->available_models['openrouter'];
        } else {
            // Cache the models for 24 hours
            set_transient('aibpg_openrouter_models', $models, 24 * HOUR_IN_SECONDS);
        }

        return $models;
    }

    /**
     * Validate OpenRouter API key
     *
     * @param string $api_key The API key to validate
     * @param string $model Optional model to validate with
     * @return array Result with valid status, message, and available models
     */
    private function validate_openrouter_key($api_key, $model = '') {
        // Log validation attempt for debugging
        error_log('Validating OpenRouter API key. Model: ' . ($model ? $model : 'default'));

        // First, get available models
        $available_models = $this->get_openrouter_models($api_key);

        // If no models were returned, there might be an issue with the API key
        if (empty($available_models)) {
            error_log('No models returned from OpenRouter');
            return array(
                'valid' => false,
                'message' => __('Could not retrieve models from OpenRouter. Please check your API key.', 'ai-blog-post-generator'),
                'models' => array()
            );
        }

        // If no model specified, use a default one
        $test_model = !empty($model) && isset($available_models[$model])
            ? $model
            : 'openai/gpt-3.5-turbo';

        // If the specified model isn't available, try to find a similar one
        if (!empty($model) && !isset($available_models[$model])) {
            // Try to find a model with a similar name
            $model_parts = explode('/', $model);
            $model_name = end($model_parts);

            foreach (array_keys($available_models) as $available_model) {
                if (strpos($available_model, $model_name) !== false) {
                    $test_model = $available_model;
                    error_log('Model not found, using similar model: ' . $test_model);
                    break;
                }
            }
        }

        // Prepare the test request
        $request_data = array(
            'headers' => array(
                'Authorization' => 'Bearer ' . $api_key,
                'Content-Type' => 'application/json',
                'HTTP-Referer' => site_url()
            ),
            'body' => json_encode(array(
                'model' => $test_model,
                'messages' => array(
                    array(
                        'role' => 'user',
                        'content' => 'Say hello'
                    )
                ),
                'max_tokens' => 10
            )),
            'timeout' => 15
        );

        // Log the request for debugging
        error_log('OpenRouter test request with model: ' . $test_model);

        // Make the request
        $response = wp_remote_post('https://openrouter.ai/api/v1/chat/completions', $request_data);

        if (is_wp_error($response)) {
            $error_message = $response->get_error_message();
            error_log('OpenRouter API Error: ' . $error_message);
            return array(
                'valid' => false,
                'message' => $error_message,
                'models' => array()
            );
        }

        $response_code = wp_remote_retrieve_response_code($response);
        $response_body = json_decode(wp_remote_retrieve_body($response), true);

        // Log the response for debugging
        error_log('OpenRouter response code: ' . $response_code);

        if ($response_code === 200) {
            // If we're testing a specific model and it worked
            if (!empty($model)) {
                if ($test_model === $model) {
                    return array(
                        'valid' => true,
                        'message' => sprintf(__('OpenRouter API key is valid and %s model is working properly.', 'ai-blog-post-generator'), $model),
                        'models' => $available_models
                    );
                } else {
                    // We used a different model than requested
                    return array(
                        'valid' => true,
                        'message' => sprintf(__('OpenRouter API key is valid. The requested model %s was not found, but %s works.', 'ai-blog-post-generator'),
                            $model, $test_model),
                        'models' => $available_models
                    );
                }
            }

            return array(
                'valid' => true,
                'message' => __('OpenRouter API key is valid.', 'ai-blog-post-generator'),
                'models' => $available_models
            );
        } else {
            $error_message = isset($response_body['error']['message'])
                ? $response_body['error']['message']
                : __('Invalid API key or model.', 'ai-blog-post-generator');

            error_log('OpenRouter validation error: ' . $error_message);

            return array(
                'valid' => false,
                'message' => $error_message,
                'models' => $available_models // Still return models if we got them
            );
        }
    }

    /**
     * Generate content using the specified AI service
     *
     * @param string $service The AI service to use
     * @param string $prompt The prompt to send to the AI
     * @param array $options Additional options for generation
     * @return array Result with success status, content, and message
     */
    public function generate_content($service, $prompt, $options = array()) {
        // For testing purposes, return mock content
        if (defined('AIBPG_TESTING') && AIBPG_TESTING) {
            // Create a mock response with proper HTML formatting and internal links
            $keyword = trim(str_replace(array('Generate a blog post about', 'Write about', 'Create content for'), '', $prompt));

            // Create a mock title
            $title = 'The Ultimate Guide to ' . ucwords($keyword);

            // Create mock content with proper HTML formatting
            $content = '<h1>' . $title . '</h1>';
            $content .= '<p>Are you looking to transform your fitness journey with ' . $keyword . '? You\'ve come to the right place. In this comprehensive guide, we\'ll explore everything you need to know about mastering ' . $keyword . ' techniques, benefits, and implementation strategies.</p>';

            // Key takeaways section
            $content .= '<h2>Key Takeaways</h2>';
            $content .= '<ul>';
            $content .= '<li>' . ucfirst($keyword) . ' is essential for optimal health and fitness, with studies showing a 45% improvement in overall physical performance</li>';
            $content .= '<li>Regular practice of ' . $keyword . ' can improve your overall wellbeing by reducing stress levels by up to 30%</li>';
            $content .= '<li>Experts recommend incorporating ' . $keyword . ' into your daily routine for at least 20 minutes per session</li>';
            $content .= '<li>The best time for ' . $keyword . ' is in the morning, as research indicates a 20% increase in metabolic benefits</li>';
            $content .= '<li>Quality equipment can enhance your ' . $keyword . ' experience and reduce injury risk by up to 60%</li>';
            $content .= '<li>Consistency is key when developing a ' . $keyword . ' habit—87% of successful practitioners maintain a regular schedule</li>';
            $content .= '</ul>';

            // Add 6 internal links with rich anchor text
            $content .= '<h2>Benefits of ' . ucwords($keyword) . '</h2>';
            $content .= '<p>Regular practice of ' . $keyword . ' offers numerous evidence-based benefits. Having the right ' . $keyword . ' gear is crucial, just like having the proper <a href="https://gearuptofit.com/fitness-essentials/">essential fitness equipment</a> for any workout routine.</p>';

            $content .= '<h2>Getting Started</h2>';
            $content .= '<p>Following <a href="https://gearuptofit.com/workout-plans/">structured workout plans</a> that incorporate ' . $keyword . ' can significantly improve your results.</p>';

            $content .= '<h2>Nutrition</h2>';
            $content .= '<p>For maximum benefits from your ' . $keyword . ' practice, follow our <a href="https://gearuptofit.com/nutrition-guide/">proper nutrition guidelines</a> to fuel your body correctly.</p>';

            $content .= '<h2>Recovery</h2>';
            $content .= '<p>After intense ' . $keyword . ' sessions, implementing <a href="https://gearuptofit.com/recovery-techniques/">effective recovery techniques</a> will help prevent injuries and improve performance.</p>';

            $content .= '<h2>Progress Tracking</h2>';
            $content .= '<p>Monitoring your ' . $keyword . ' journey using reliable <a href="https://gearuptofit.com/fitness-tracking/">progress tracking methods</a> helps maintain motivation and identify areas for improvement.</p>';

            $content .= '<h2>Advanced Techniques</h2>';
            $content .= '<p>For advanced ' . $keyword . ' practitioners, our <a href="https://gearuptofit.com/expert-advice/">expert fitness advice</a> provides valuable insights to take your practice to the next level.</p>';

            return array(
                'success' => true,
                'content' => $content,
                'message' => 'Mock content generated successfully with proper formatting and internal links.'
            );
        }

        // Log the generation request
        error_log(sprintf('Content generation request for service: %s', $service));

        // Get the API key using our secure method
        $api_key = $this->get_api_key($service);

        if (empty($api_key)) {
            error_log(sprintf('No valid API key found for service: %s', $service));
            return array(
                'success' => false,
                'message' => sprintf(__('No valid API key found for %s. Please configure your API key in the settings.', 'ai-blog-post-generator'), $service)
            );
        }

        // Validate prompt
        if (empty($prompt)) {
            error_log('Empty prompt provided for content generation');
            return array(
                'success' => false,
                'message' => __('Empty prompt provided. Please provide a valid prompt for content generation.', 'ai-blog-post-generator')
            );
        }

        // Add request tracking and check rate limits
        $tracking_result = $this->track_api_request($service);
        if (!$tracking_result) {
            // If tracking returns false, we've hit a rate limit
            return array(
                'success' => false,
                'message' => sprintf(__('Rate limit reached for %s service. Please try again later.', 'ai-blog-post-generator'), $service)
            );
        }

        // Double-check with legacy rate limit method
        $rate_limit_status = $this->check_rate_limit($service);
        if (!$rate_limit_status['allowed']) {
            return array(
                'success' => false,
                'message' => $rate_limit_status['message']
            );
        }

        // Generate content with the appropriate service
        $result = array('success' => false, 'message' => __('Unknown error occurred.', 'ai-blog-post-generator'));

        try {
            switch ($service) {
                case 'openai':
                    $result = $this->generate_with_openai($api_key, $prompt);
                    break;
                case 'claude':
                    $result = $this->generate_with_claude($api_key, $prompt);
                    break;
                case 'gemini':
                    $result = $this->generate_with_gemini($api_key, $prompt);
                    break;
                case 'openrouter':
                    $result = $this->generate_with_openrouter($api_key, $prompt);
                    break;
                default:
                    $result = array(
                        'success' => false,
                        'message' => __('Invalid service.', 'ai-blog-post-generator')
                    );
            }
        } catch (Exception $e) {
            $result = array(
                'success' => false,
                'message' => sprintf(__('Error: %s', 'ai-blog-post-generator'), $e->getMessage())
            );
        }

        // Track the result
        $this->track_api_result($service, $result['success']);

        return $result;
    }

    /**
     * Track API request
     *
     * @param string $service The AI service
     * @return bool Whether the request is allowed (not rate limited)
     */
    private function track_api_request($service) {
        $requests = get_option('aibpg_api_requests', array());
        $current_time = time();
        $hourly_limit = 10; // Default hourly limit
        $daily_limit = 50;  // Default daily limit

        // Initialize if not set
        if (!isset($requests[$service])) {
            $requests[$service] = array(
                'total' => 0,
                'success' => 0,
                'failure' => 0,
                'last_request' => 0,
                'hourly_count' => 0,
                'daily_count' => 0,
                'hourly_reset' => $current_time + 3600,
                'daily_reset' => $current_time + 86400
            );
        }

        // Check if hourly counter needs reset
        if ($current_time > $requests[$service]['hourly_reset']) {
            $requests[$service]['hourly_count'] = 0;
            $requests[$service]['hourly_reset'] = $current_time + 3600;
            error_log(sprintf('Hourly counter reset for %s service', $service));
        }

        // Check if daily counter needs reset
        if ($current_time > $requests[$service]['daily_reset']) {
            $requests[$service]['daily_count'] = 0;
            $requests[$service]['daily_reset'] = $current_time + 86400;
            error_log(sprintf('Daily counter reset for %s service', $service));
        }

        // Check rate limits
        if (isset($requests[$service]['hourly_count']) && $requests[$service]['hourly_count'] >= $hourly_limit) {
            error_log(sprintf('Rate limit exceeded for %s: hourly limit of %d requests reached', $service, $hourly_limit));
            return false;
        }

        if (isset($requests[$service]['daily_count']) && $requests[$service]['daily_count'] >= $daily_limit) {
            error_log(sprintf('Rate limit exceeded for %s: daily limit of %d requests reached', $service, $daily_limit));
            return false;
        }

        // Update tracking
        $requests[$service]['total']++;
        $requests[$service]['last_request'] = $current_time;

        // Update rate limit counters
        if (!isset($requests[$service]['hourly_count'])) {
            $requests[$service]['hourly_count'] = 0;
        }
        if (!isset($requests[$service]['daily_count'])) {
            $requests[$service]['daily_count'] = 0;
        }

        $requests[$service]['hourly_count']++;
        $requests[$service]['daily_count']++;

        // Save tracking data
        update_option('aibpg_api_requests', $requests);

        return true;
    }

    /**
     * Track API result
     *
     * @param string $service The AI service
     * @param bool $success Whether the request was successful
     */
    private function track_api_result($service, $success) {
        $requests = get_option('aibpg_api_requests', array());

        if (!isset($requests[$service])) {
            return;
        }

        if ($success) {
            $requests[$service]['success']++;
        } else {
            $requests[$service]['failure']++;
        }

        update_option('aibpg_api_requests', $requests);
    }

    /**
     * Check rate limit for API requests
     *
     * @param string $service The AI service
     * @return array Status with allowed flag and message
     */
    private function check_rate_limit($service) {
        // Get rate limit settings
        $rate_limits = array(
            'openai' => array('requests_per_minute' => 20, 'requests_per_day' => 1000),
            'claude' => array('requests_per_minute' => 15, 'requests_per_day' => 500),
            'gemini' => array('requests_per_minute' => 30, 'requests_per_day' => 1500),
            'openrouter' => array('requests_per_minute' => 10, 'requests_per_day' => 300)
        );

        // Default limits if service not found
        $limits = isset($rate_limits[$service]) ? $rate_limits[$service] : array('requests_per_minute' => 10, 'requests_per_day' => 200);

        // Get request history
        $requests = get_option('aibpg_api_requests', array());

        if (!isset($requests[$service])) {
            return array('allowed' => true, 'message' => '');
        }

        // Check hourly limit (from our improved tracking)
        if (isset($requests[$service]['hourly_count']) && $requests[$service]['hourly_count'] >= $limits['requests_per_minute'] * 3) {
            $minutes_to_reset = ceil(($requests[$service]['hourly_reset'] - time()) / 60);
            return array(
                'allowed' => false,
                'message' => sprintf(__('Hourly limit reached for %s. Please try again in %d minutes.', 'ai-blog-post-generator'),
                    $service,
                    $minutes_to_reset
                )
            );
        }

        // Check daily limit (from our improved tracking)
        if (isset($requests[$service]['daily_count']) && $requests[$service]['daily_count'] >= $limits['requests_per_day']) {
            $hours_to_reset = ceil(($requests[$service]['daily_reset'] - time()) / 3600);
            return array(
                'allowed' => false,
                'message' => sprintf(__('Daily limit of %d requests for %s has been reached. Please try again in %d hours.', 'ai-blog-post-generator'),
                    $limits['requests_per_day'],
                    $service,
                    $hours_to_reset
                )
            );
        }

        // Check minute limit (legacy method, kept for backward compatibility)
        $minute_requests = get_transient('aibpg_minute_api_requests_' . $service);
        if ($minute_requests === false) {
            $minute_requests = 0;
        }

        if ($minute_requests >= $limits['requests_per_minute']) {
            return array(
                'allowed' => false,
                'message' => sprintf(__('Rate limit of %d requests per minute for %s has been reached. Please try again shortly.', 'ai-blog-post-generator'),
                    $limits['requests_per_minute'],
                    $service
                )
            );
        }

        // Increment minute counter (legacy method)
        $minute_requests++;
        set_transient('aibpg_minute_api_requests_' . $service, $minute_requests, 60);

        return array('allowed' => true, 'message' => '');
    }

    /**
     * Generate content with OpenAI
     *
     * @param string $api_key The API key
     * @param string $prompt The prompt
     * @return array Result with success status, content, and message
     */
    private function generate_with_openai($api_key, $prompt) {
        $response = wp_remote_post('https://api.openai.com/v1/chat/completions', array(
            'headers' => array(
                'Authorization' => 'Bearer ' . $api_key,
                'Content-Type' => 'application/json'
            ),
            'body' => json_encode(array(
                'model' => 'gpt-4',
                'messages' => array(
                    array(
                        'role' => 'system',
                        'content' => 'You are an expert AI Content Strategist and Writer, specializing in creating deeply human, engaging, and SEO-optimized content for the health and fitness niche.'
                    ),
                    array(
                        'role' => 'user',
                        'content' => $prompt
                    )
                ),
                'max_tokens' => 4000,
                'temperature' => 0.7
            )),
            'timeout' => 120
        ));

        if (is_wp_error($response)) {
            return array(
                'success' => false,
                'message' => $response->get_error_message()
            );
        }

        $response_code = wp_remote_retrieve_response_code($response);
        $response_body = json_decode(wp_remote_retrieve_body($response), true);

        if ($response_code === 200) {
            $content = $response_body['choices'][0]['message']['content'];

            return array(
                'success' => true,
                'content' => $content,
                'message' => __('Content generated successfully.', 'ai-blog-post-generator')
            );
        } else {
            $error_message = isset($response_body['error']['message'])
                ? $response_body['error']['message']
                : __('Failed to generate content.', 'ai-blog-post-generator');

            return array(
                'success' => false,
                'message' => $error_message
            );
        }
    }

    /**
     * Generate content with Claude
     *
     * @param string $api_key The API key
     * @param string $prompt The prompt
     * @return array Result with success status, content, and message
     */
    private function generate_with_claude($api_key, $prompt) {
        $response = wp_remote_post('https://api.anthropic.com/v1/messages', array(
            'headers' => array(
                'x-api-key' => $api_key,
                'anthropic-version' => '2023-06-01',
                'Content-Type' => 'application/json'
            ),
            'body' => json_encode(array(
                'model' => 'claude-3-opus-20240229',
                'max_tokens' => 4000,
                'messages' => array(
                    array(
                        'role' => 'user',
                        'content' => $prompt
                    )
                ),
                'system' => 'You are an expert AI Content Strategist and Writer, specializing in creating deeply human, engaging, and SEO-optimized content for the health and fitness niche.'
            )),
            'timeout' => 120
        ));

        if (is_wp_error($response)) {
            return array(
                'success' => false,
                'message' => $response->get_error_message()
            );
        }

        $response_code = wp_remote_retrieve_response_code($response);
        $response_body = json_decode(wp_remote_retrieve_body($response), true);

        if ($response_code === 200 || $response_code === 201) {
            $content = $response_body['content'][0]['text'];

            return array(
                'success' => true,
                'content' => $content,
                'message' => __('Content generated successfully.', 'ai-blog-post-generator')
            );
        } else {
            $error_message = isset($response_body['error']['message'])
                ? $response_body['error']['message']
                : __('Failed to generate content.', 'ai-blog-post-generator');

            return array(
                'success' => false,
                'message' => $error_message
            );
        }
    }

    /**
     * Generate content with Gemini
     *
     * @param string $api_key The API key
     * @param string $prompt The prompt
     * @return array Result with success status, content, and message
     */
    private function generate_with_gemini($api_key, $prompt) {
        // Get the preferred model
        $preferred_model = get_option('aibpg_gemini_preferred_model', 'gemini-pro');

        // Log the model being used
        error_log('Attempting to generate content with Gemini model: ' . $preferred_model);

        // Try with the preferred model first
        $response = $this->generate_with_gemini_model($api_key, $prompt, $preferred_model);

        // If that fails and we're not already using gemini-pro, try with gemini-pro
        if (!$response['success'] && $preferred_model !== 'gemini-pro') {
            error_log('Falling back to gemini-pro model');
            $response = $this->generate_with_gemini_model($api_key, $prompt, 'gemini-pro');
        }

        return $response;
    }

    /**
     * Generate content with a specific Gemini model
     *
     * @param string $api_key The API key
     * @param string $prompt The prompt
     * @param string $model The model to use
     * @return array Result with success status, content, and message
     */
    private function generate_with_gemini_model($api_key, $prompt, $model = 'gemini-pro') {
        $system_prompt = 'You are an expert AI Content Strategist and Writer, specializing in creating deeply human, engaging, and SEO-optimized content for the health and fitness niche.';

        // Log the request
        error_log('Making Gemini API request with model: ' . $model);

        // Build the API URL
        $api_url = "https://generativelanguage.googleapis.com/v1/models/{$model}:generateContent?key=" . $api_key;

        // Build the request body
        $request_body = array(
            'contents' => array(
                array(
                    'role' => 'user',
                    'parts' => array(
                        array(
                            'text' => $system_prompt . ' ' . $prompt
                        )
                    )
                )
            ),
            'generationConfig' => array(
                'temperature' => 0.7,
                'maxOutputTokens' => 4000,
                'topK' => 40,
                'topP' => 0.95
            ),
            'safetySettings' => array(
                array(
                    'category' => 'HARM_CATEGORY_HARASSMENT',
                    'threshold' => 'BLOCK_MEDIUM_AND_ABOVE'
                ),
                array(
                    'category' => 'HARM_CATEGORY_HATE_SPEECH',
                    'threshold' => 'BLOCK_MEDIUM_AND_ABOVE'
                ),
                array(
                    'category' => 'HARM_CATEGORY_SEXUALLY_EXPLICIT',
                    'threshold' => 'BLOCK_MEDIUM_AND_ABOVE'
                ),
                array(
                    'category' => 'HARM_CATEGORY_DANGEROUS_CONTENT',
                    'threshold' => 'BLOCK_MEDIUM_AND_ABOVE'
                )
            )
        );

        // Make the API request
        $response = wp_remote_post($api_url, array(
            'headers' => array(
                'Content-Type' => 'application/json'
            ),
            'body' => json_encode($request_body),
            'timeout' => 120
        ));

        // Handle errors
        if (is_wp_error($response)) {
            $error_message = $response->get_error_message();
            error_log('Gemini API error: ' . $error_message);
            return array(
                'success' => false,
                'message' => $error_message
            );
        }

        // Process the response
        $response_code = wp_remote_retrieve_response_code($response);
        $response_body = json_decode(wp_remote_retrieve_body($response), true);

        // Log the response code
        error_log('Gemini API response code: ' . $response_code);

        // If there's an error, log it
        if ($response_code !== 200) {
            $error_message = isset($response_body['error']['message'])
                ? $response_body['error']['message']
                : 'Unknown error occurred';
            error_log('Gemini API error: ' . $error_message);

            if (isset($response_body['error'])) {
                error_log('Gemini API error details: ' . json_encode($response_body['error']));
            }
        }

        // Check if we have a valid response
        if ($response_code === 200 && isset($response_body['candidates'][0]['content']['parts'][0]['text'])) {
            $content = $response_body['candidates'][0]['content']['parts'][0]['text'];

            return array(
                'success' => true,
                'content' => $content,
                'message' => sprintf(__('Content generated successfully with Gemini model: %s', 'ai-blog-post-generator'), $model)
            );
        } else {
            $error_message = isset($response_body['error']['message'])
                ? $response_body['error']['message']
                : sprintf(__('Failed to generate content with Gemini model: %s', 'ai-blog-post-generator'), $model);

            return array(
                'success' => false,
                'message' => $error_message
            );
        }
    }

    /**
     * Generate content with OpenRouter
     *
     * @param string $api_key The API key
     * @param string $prompt The prompt
     * @return array Result with success status, content, and message
     */
    private function generate_with_openrouter($api_key, $prompt) {
        $response = wp_remote_post('https://openrouter.ai/api/v1/chat/completions', array(
            'headers' => array(
                'Authorization' => 'Bearer ' . $api_key,
                'Content-Type' => 'application/json',
                'HTTP-Referer' => site_url()
            ),
            'body' => json_encode(array(
                'model' => 'anthropic/claude-3-opus',
                'messages' => array(
                    array(
                        'role' => 'system',
                        'content' => 'You are an expert AI Content Strategist and Writer, specializing in creating deeply human, engaging, and SEO-optimized content for the health and fitness niche.'
                    ),
                    array(
                        'role' => 'user',
                        'content' => $prompt
                    )
                ),
                'max_tokens' => 4000,
                'temperature' => 0.7
            )),
            'timeout' => 120
        ));

        if (is_wp_error($response)) {
            return array(
                'success' => false,
                'message' => $response->get_error_message()
            );
        }

        $response_code = wp_remote_retrieve_response_code($response);
        $response_body = json_decode(wp_remote_retrieve_body($response), true);

        if ($response_code === 200) {
            $content = $response_body['choices'][0]['message']['content'];

            return array(
                'success' => true,
                'content' => $content,
                'message' => __('Content generated successfully.', 'ai-blog-post-generator')
            );
        } else {
            $error_message = isset($response_body['error']['message'])
                ? $response_body['error']['message']
                : __('Failed to generate content.', 'ai-blog-post-generator');

            return array(
                'success' => false,
                'message' => $error_message
            );
        }
    }
}
