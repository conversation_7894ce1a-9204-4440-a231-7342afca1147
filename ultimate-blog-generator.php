<?php
/**
 * Plugin Name: Ultimate Blog Generator Pro
 * Plugin URI: https://example.com/ultimate-blog-generator-pro
 * Description: The most advanced blog post generator with AI-powered content, rich internal linking, high-quality images, and authoritative references
 * Version: 2.0.0
 * Requires at least: 5.6
 * Requires PHP: 7.2
 * Author: Augment Agent
 * Author URI: https://example.com
 * License: GPL v2 or later
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 * Text Domain: ultimate-blog-generator
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('UBG_VERSION', '2.0.0');
define('UBG_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('UBG_PLUGIN_URL', plugin_dir_url(__FILE__));

// Add admin menu
add_action('admin_menu', 'ubg_add_menu');

function ubg_add_menu() {
    add_menu_page(
        'Ultimate Blog Generator Pro',
        'Ultimate Blog Generator',
        'manage_options',
        'ultimate-blog-generator',
        'ubg_admin_page',
        'dashicons-edit',
        30
    );
}

// Enqueue admin scripts and styles
add_action('admin_enqueue_scripts', 'ubg_enqueue_scripts');

function ubg_enqueue_scripts($hook) {
    if ($hook != 'toplevel_page_ultimate-blog-generator') {
        return;
    }

    // Enqueue WordPress media library
    wp_enqueue_media();

    // Add custom CSS
    wp_add_inline_style('wp-admin', '
        .ubg-admin-wrap { max-width: 1200px; margin: 20px auto; }
        .ubg-page-title { display: flex; align-items: center; margin-bottom: 20px; color: #2271b1; }
        .ubg-page-title .dashicons { margin-right: 10px; font-size: 24px; }
        .ubg-version { font-size: 12px; color: #666; margin-left: 10px; font-weight: normal; }
        .ubg-tab-content { margin-top: 0; border: 1px solid #c3c4c7; border-top: none; background: #fff; padding: 20px; }
        .ubg-form-field { margin-bottom: 20px; }
        .ubg-form-field label { display: block; margin-bottom: 5px; font-weight: bold; }
        .ubg-form-row { display: flex; gap: 20px; margin-bottom: 15px; }
        .ubg-form-row > div { flex: 1; }
        .ubg-preview-body { padding: 20px; background: #f9f9f9; border-radius: 4px; margin: 20px 0; }
        .ubg-image-placeholder { background: #e0e0e0; padding: 20px; text-align: center; margin: 15px 0; border-radius: 4px; }
        .ubg-references { background: #f0f6ff; padding: 20px; border-left: 4px solid #2271b1; margin: 20px 0; }
        .ubg-notification { position: fixed; top: 50px; right: 20px; z-index: 9999; padding: 15px; border-radius: 4px; box-shadow: 0 2px 5px rgba(0,0,0,0.2); }
        .ubg-notification.success { background: #00a32a; color: white; }
        .ubg-notification.error { background: #d63638; color: white; }
        .ubg-loading { opacity: 0.6; pointer-events: none; }
    ');

    // Localize script with data
    wp_localize_script('jquery', 'ubg_params', array(
        'ajax_url' => admin_url('admin-ajax.php'),
        'nonce' => wp_create_nonce('ubg_nonce'),
        'researching_text' => 'Researching Keywords...',
        'generating_text' => 'Generating Premium Content...',
        'saving_text' => 'Saving Post...'
    ));
}

// Admin page content
function ubg_admin_page() {
    // Get categories
    $categories = get_categories(array(
        'hide_empty' => false,
        'orderby' => 'name',
        'order' => 'ASC'
    ));

    // Get tags
    $tags = get_tags(array(
        'hide_empty' => false,
        'orderby' => 'name',
        'order' => 'ASC'
    ));

    // Get existing posts for internal linking
    $existing_posts = get_posts(array(
        'posts_per_page' => 50,
        'post_status' => 'publish',
        'orderby' => 'date',
        'order' => 'DESC'
    ));
    ?>
    <div class="wrap ubg-admin-wrap">
        <h1 class="ubg-page-title">
            <span class="dashicons dashicons-edit"></span> Ultimate Blog Generator Pro
            <span class="ubg-version">v<?php echo UBG_VERSION; ?></span>
        </h1>

        <div class="nav-tab-wrapper">
            <a href="#auto-research" class="nav-tab nav-tab-active" id="tab-auto-research">🔍 Auto Research</a>
            <a href="#manual-keyword" class="nav-tab" id="tab-manual-keyword">✍️ Manual Keyword</a>
            <a href="#settings" class="nav-tab" id="tab-settings">⚙️ Settings</a>
        </div>

        <!-- Auto Research Tab -->
        <div class="tab-content ubg-tab-content" id="auto-research" style="display: block;">
            <div class="postbox">
                <div class="inside">
                    <h2>🎯 Advanced Keyword Research</h2>
                    <p>Enter a niche to discover high-converting keywords with comprehensive SEO metrics and competition analysis.</p>

                    <div class="ubg-form-field">
                        <label for="ubg-niche">Target Niche:</label>
                        <input type="text" id="ubg-niche" class="regular-text" placeholder="e.g., sustainable living, digital marketing, personal finance">
                        <p class="description">Enter a specific niche or industry for targeted keyword research.</p>
                    </div>

                    <div class="ubg-form-row">
                        <div>
                            <label for="ubg-country">Target Country:</label>
                            <select id="ubg-country" class="regular-text">
                                <option value="US">United States</option>
                                <option value="UK">United Kingdom</option>
                                <option value="CA">Canada</option>
                                <option value="AU">Australia</option>
                                <option value="DE">Germany</option>
                            </select>
                        </div>
                        <div>
                            <label for="ubg-language">Content Language:</label>
                            <select id="ubg-language" class="regular-text">
                                <option value="en">English</option>
                                <option value="es">Spanish</option>
                                <option value="fr">French</option>
                                <option value="de">German</option>
                                <option value="it">Italian</option>
                            </select>
                        </div>
                    </div>

                    <div>
                        <button id="ubg-research-button" class="button button-primary button-large">
                            🔍 Research Premium Keywords
                        </button>
                    </div>

                    <div id="ubg-research-results" style="display: none; margin-top: 30px;">
                        <h3>🎯 Premium Keyword Opportunities</h3>
                        <table class="widefat striped">
                            <thead>
                                <tr>
                                    <th>Keyword</th>
                                    <th>Search Volume</th>
                                    <th>Difficulty</th>
                                    <th>CPC</th>
                                    <th>Competition</th>
                                    <th>Trend</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody id="ubg-keywords-list">
                                <!-- Keywords will be added here dynamically -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Manual Keyword Tab -->
        <div class="tab-content ubg-tab-content" id="manual-keyword" style="display: none;">
            <div class="postbox">
                <div class="inside">
                    <h2>✍️ Premium Content Generation</h2>
                    <p>Create high-quality, SEO-optimized content with rich internal linking, professional images, and authoritative references.</p>

                    <div class="ubg-form-field">
                        <label for="ubg-keyword">Primary Keyword:</label>
                        <input type="text" id="ubg-keyword" class="regular-text" placeholder="e.g., sustainable home design, email marketing automation">
                        <p class="description">Enter your target keyword for content optimization.</p>
                    </div>

                    <div class="ubg-form-row">
                        <div>
                            <label for="ubg-category">Category:</label>
                            <select id="ubg-category" class="regular-text">
                                <option value="0">Select a category</option>
                                <?php foreach ($categories as $category) : ?>
                                    <option value="<?php echo esc_attr($category->term_id); ?>"><?php echo esc_html($category->name); ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div>
                            <label for="ubg-post-status">Post Status:</label>
                            <select id="ubg-post-status" class="regular-text">
                                <option value="draft">Draft</option>
                                <option value="publish">Publish</option>
                                <option value="pending">Pending Review</option>
                                <option value="private">Private</option>
                            </select>
                        </div>
                    </div>

                    <div class="ubg-form-row">
                        <div>
                            <label for="ubg-tone">Content Tone:</label>
                            <select id="ubg-tone" class="regular-text">
                                <option value="professional">Professional & Authoritative</option>
                                <option value="conversational">Conversational & Engaging</option>
                                <option value="educational">Educational & Informative</option>
                                <option value="persuasive">Persuasive & Action-Oriented</option>
                                <option value="storytelling">Storytelling & Narrative</option>
                            </select>
                        </div>

                        <div>
                            <label for="ubg-length">Content Length:</label>
                            <select id="ubg-length" class="regular-text">
                                <option value="comprehensive">Comprehensive (2000+ words)</option>
                                <option value="detailed" selected>Detailed (1500-2000 words)</option>
                                <option value="standard">Standard (1000-1500 words)</option>
                                <option value="concise">Concise (800-1000 words)</option>
                            </select>
                        </div>
                    </div>

                    <div class="ubg-form-row">
                        <div>
                            <label for="ubg-audience">Target Audience:</label>
                            <select id="ubg-audience" class="regular-text">
                                <option value="general">General Audience</option>
                                <option value="beginners">Beginners</option>
                                <option value="intermediate">Intermediate</option>
                                <option value="experts">Experts/Professionals</option>
                                <option value="business">Business Owners</option>
                            </select>
                        </div>

                        <div>
                            <label for="ubg-content-type">Content Type:</label>
                            <select id="ubg-content-type" class="regular-text">
                                <option value="guide">Complete Guide</option>
                                <option value="howto">How-To Tutorial</option>
                                <option value="listicle">List Article</option>
                                <option value="comparison">Comparison/Review</option>
                                <option value="analysis">Analysis/Opinion</option>
                            </select>
                        </div>
                    </div>

                    <div class="ubg-form-field">
                        <label for="ubg-tags">Tags:</label>
                        <select id="ubg-tags" class="regular-text" multiple>
                            <?php foreach ($tags as $tag) : ?>
                                <option value="<?php echo esc_attr($tag->term_id); ?>"><?php echo esc_html($tag->name); ?></option>
                            <?php endforeach; ?>
                        </select>
                        <p class="description">Hold Ctrl/Cmd to select multiple tags.</p>
                    </div>

                    <div>
                        <button id="ubg-generate-button" class="button button-primary button-large">
                            🚀 Generate Premium Content
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Settings Tab -->
        <div class="tab-content ubg-tab-content" id="settings" style="display: none;">
            <div class="postbox">
                <div class="inside">
                    <h2>⚙️ Advanced Settings</h2>
                    <p>Configure advanced options for premium content generation and SEO optimization.</p>

                    <div class="ubg-form-row">
                        <div>
                            <label for="ubg-default-author">Default Author:</label>
                            <select id="ubg-default-author" class="regular-text">
                                <?php
                                $users = get_users(array('role__in' => array('administrator', 'editor', 'author')));
                                foreach ($users as $user) {
                                    echo '<option value="' . esc_attr($user->ID) . '">' . esc_html($user->display_name) . '</option>';
                                }
                                ?>
                            </select>
                        </div>

                        <div>
                            <label for="ubg-seo-optimization">SEO Optimization Level:</label>
                            <select id="ubg-seo-optimization" class="regular-text">
                                <option value="basic">Basic SEO</option>
                                <option value="advanced" selected>Advanced SEO</option>
                                <option value="expert">Expert SEO</option>
                                <option value="premium">Premium SEO</option>
                            </select>
                        </div>
                    </div>

                    <div class="ubg-form-row">
                        <div>
                            <label for="ubg-internal-links">Internal Links Count:</label>
                            <input type="number" id="ubg-internal-links" class="small-text" value="6" min="3" max="15">
                            <p class="description">Number of internal links to include (3-15).</p>
                        </div>

                        <div>
                            <label for="ubg-external-references">External References:</label>
                            <input type="number" id="ubg-external-references" class="small-text" value="8" min="5" max="15">
                            <p class="description">Number of authoritative references (5-15).</p>
                        </div>
                    </div>

                    <div class="ubg-form-row">
                        <div>
                            <label for="ubg-images-count">High-Quality Images:</label>
                            <input type="number" id="ubg-images-count" class="small-text" value="3" min="1" max="10">
                            <p class="description">Number of professional images to include.</p>
                        </div>

                        <div>
                            <label for="ubg-readability">Readability Level:</label>
                            <select id="ubg-readability" class="regular-text">
                                <option value="simple">Simple (Grade 6-8)</option>
                                <option value="standard" selected>Standard (Grade 9-12)</option>
                                <option value="advanced">Advanced (College Level)</option>
                            </select>
                        </div>
                    </div>

                    <div class="ubg-form-field">
                        <label>
                            <input type="checkbox" id="ubg-featured-image" checked>
                            Auto-generate featured image with AI-powered suggestions
                        </label>
                    </div>

                    <div class="ubg-form-field">
                        <label>
                            <input type="checkbox" id="ubg-meta-description" checked>
                            Generate SEO-optimized meta description
                        </label>
                    </div>

                    <div class="ubg-form-field">
                        <label>
                            <input type="checkbox" id="ubg-schema-markup" checked>
                            Include structured data (Schema.org markup)
                        </label>
                    </div>

                    <div>
                        <button id="ubg-save-settings-button" class="button button-primary">
                            💾 Save Advanced Settings
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Preview Section -->
        <div id="ubg-preview" class="postbox" style="display: none; margin-top: 30px;">
            <div class="inside">
                <h2>📝 Premium Content Preview</h2>

                <div class="ubg-preview-content">
                    <div style="margin-bottom: 20px;">
                        <h3 id="ubg-preview-title" style="color: #2271b1; font-size: 24px;"></h3>
                        <div id="ubg-meta-description-preview" style="color: #666; font-style: italic; margin-top: 10px;"></div>
                    </div>

                    <div id="ubg-preview-body" class="ubg-preview-body">
                        <!-- Generated content will be added here -->
                    </div>

                    <div id="ubg-seo-analysis" style="display: none; background: #e7f3ff; padding: 20px; border-radius: 4px; margin: 20px 0;">
                        <h3>📊 SEO Analysis</h3>
                        <div id="ubg-seo-metrics">
                            <!-- SEO metrics will be added here -->
                        </div>
                    </div>

                    <div style="display: flex; gap: 10px; flex-wrap: wrap; margin-top: 30px;">
                        <button id="ubg-save-button" class="button button-primary button-large">
                            💾 Save Premium Post
                        </button>
                        <button id="ubg-copy-button" class="button button-large">
                            📋 Copy Content
                        </button>
                        <button id="ubg-edit-button" class="button button-large">
                            ✏️ Edit Content
                        </button>
                        <button id="ubg-reset-button" class="button button-large">
                            🔄 Reset
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script type="text/javascript">
        jQuery(document).ready(function($) {
            // Tab navigation
            $('.nav-tab').on('click', function(e) {
                e.preventDefault();

                var target = $(this).attr('href').substring(1);

                // Remove active class from all tabs
                $('.nav-tab').removeClass('nav-tab-active');
                $('.tab-content').hide();

                // Add active class to clicked tab
                $(this).addClass('nav-tab-active');
                $('#' + target).show();
            });

            // Show notification function
            function showNotification(message, type) {
                var notification = $('<div class="ubg-notification ' + type + '">' + message + '</div>');
                $('body').append(notification);

                setTimeout(function() {
                    notification.fadeOut(300, function() {
                        $(this).remove();
                    });
                }, 4000);
            }

            // Research button click handler
            $('#ubg-research-button').on('click', function() {
                var niche = $('#ubg-niche').val().trim();
                var country = $('#ubg-country').val();
                var language = $('#ubg-language').val();

                if (!niche) {
                    showNotification('Please enter a niche to research', 'error');
                    return;
                }

                // Show loading state
                $(this).prop('disabled', true).html('🔍 ' + ubg_params.researching_text);
                $('.ubg-admin-wrap').addClass('ubg-loading');

                // Make AJAX request
                $.ajax({
                    url: ubg_params.ajax_url,
                    type: 'POST',
                    data: {
                        action: 'ubg_research_keywords',
                        nonce: ubg_params.nonce,
                        niche: niche,
                        country: country,
                        language: language
                    },
                    success: function(response) {
                        if (response.success) {
                            // Populate keywords table
                            var $tbody = $('#ubg-keywords-list');
                            $tbody.empty();

                            $.each(response.data.keywords, function(index, keyword) {
                                var trendIcon = keyword.trend === 'up' ? '📈' : keyword.trend === 'down' ? '📉' : '➡️';
                                var difficultyColor = keyword.difficulty === 'Low' ? '#00a32a' : keyword.difficulty === 'Medium' ? '#dba617' : '#d63638';

                                var row = '<tr>';
                                row += '<td><strong>' + keyword.keyword + '</strong></td>';
                                row += '<td>' + keyword.search_volume + '</td>';
                                row += '<td><span style="color: ' + difficultyColor + '">' + keyword.difficulty + '</span></td>';
                                row += '<td>' + keyword.cpc + '</td>';
                                row += '<td>' + keyword.competition + '</td>';
                                row += '<td>' + trendIcon + ' ' + keyword.trend_text + '</td>';
                                row += '<td><button class="button button-primary ubg-select-keyword" data-keyword="' + keyword.keyword + '">✅ Select</button></td>';
                                row += '</tr>';

                                $tbody.append(row);
                            });

                            // Show results
                            $('#ubg-research-results').fadeIn();

                            showNotification('Premium keyword research completed successfully! 🎯', 'success');
                        } else {
                            showNotification(response.data.message, 'error');
                        }
                    },
                    error: function() {
                        showNotification('An error occurred while researching keywords', 'error');
                    },
                    complete: function() {
                        // Reset button state
                        $('#ubg-research-button').prop('disabled', false).html('🔍 Research Premium Keywords');
                        $('.ubg-admin-wrap').removeClass('ubg-loading');
                    }
                });
            });

            // Keyword selection from research results
            $(document).on('click', '.ubg-select-keyword', function() {
                var keyword = $(this).data('keyword');

                // Switch to manual keyword tab
                $('#tab-manual-keyword').trigger('click');

                // Set the keyword
                $('#ubg-keyword').val(keyword);

                showNotification('Keyword selected: ' + keyword + ' 🎯', 'success');
            });

            // Generate button click handler
            $('#ubg-generate-button').on('click', function() {
                var keyword = $('#ubg-keyword').val().trim();

                if (!keyword) {
                    showNotification('Please enter a keyword to generate premium content', 'error');
                    return;
                }

                // Show loading state
                $(this).prop('disabled', true).html('🚀 ' + ubg_params.generating_text);
                $('.ubg-admin-wrap').addClass('ubg-loading');

                // Make AJAX request
                $.ajax({
                    url: ubg_params.ajax_url,
                    type: 'POST',
                    data: {
                        action: 'ubg_generate_post',
                        nonce: ubg_params.nonce,
                        keyword: keyword,
                        tone: $('#ubg-tone').val(),
                        length: $('#ubg-length').val(),
                        audience: $('#ubg-audience').val(),
                        content_type: $('#ubg-content-type').val(),
                        internal_links: $('#ubg-internal-links').val(),
                        external_references: $('#ubg-external-references').val(),
                        images_count: $('#ubg-images-count').val()
                    },
                    success: function(response) {
                        if (response.success) {
                            // Populate preview
                            $('#ubg-preview-title').text(response.data.title);
                            $('#ubg-meta-description-preview').text('Meta Description: ' + response.data.meta_description);
                            $('#ubg-preview-body').html(response.data.content);

                            // Show SEO analysis
                            $('#ubg-seo-metrics').html(response.data.seo_analysis);
                            $('#ubg-seo-analysis').fadeIn();

                            // Show preview
                            $('#ubg-preview').fadeIn();

                            // Scroll to preview
                            $('html, body').animate({
                                scrollTop: $('#ubg-preview').offset().top - 50
                            }, 500);

                            showNotification('Premium content generated successfully! 🚀', 'success');
                        } else {
                            showNotification(response.data.message, 'error');
                        }
                    },
                    error: function() {
                        showNotification('An error occurred while generating premium content', 'error');
                    },
                    complete: function() {
                        // Reset button state
                        $('#ubg-generate-button').prop('disabled', false).html('🚀 Generate Premium Content');
                        $('.ubg-admin-wrap').removeClass('ubg-loading');
                    }
                });
            });

            // Save button click handler
            $('#ubg-save-button').on('click', function() {
                var title = $('#ubg-preview-title').text();
                var content = $('#ubg-preview-body').html();
                var category = $('#ubg-category').val();
                var postStatus = $('#ubg-post-status').val();
                var tags = $('#ubg-tags').val();

                if (!title || !content) {
                    showNotification('No content to save', 'error');
                    return;
                }

                // Show loading state
                $(this).prop('disabled', true).html('💾 ' + ubg_params.saving_text);

                // Make AJAX request
                $.ajax({
                    url: ubg_params.ajax_url,
                    type: 'POST',
                    data: {
                        action: 'ubg_save_post',
                        nonce: ubg_params.nonce,
                        title: title,
                        content: content,
                        category: category,
                        post_status: postStatus,
                        tags: tags,
                        meta_description: $('#ubg-meta-description-preview').text().replace('Meta Description: ', '')
                    },
                    success: function(response) {
                        if (response.success) {
                            showNotification('Premium post saved successfully! 🎉', 'success');

                            // Add edit and view links
                            var editLink = '<a href="' + response.data.edit_url + '" class="button button-large" target="_blank">✏️ Edit Post</a>';
                            var viewLink = '<a href="' + response.data.view_url + '" class="button button-large" target="_blank">👁️ View Post</a>';

                            $('#ubg-save-button').parent().append(' ' + editLink + ' ' + viewLink);
                        } else {
                            showNotification(response.data.message, 'error');
                        }
                    },
                    error: function() {
                        showNotification('An error occurred while saving the premium post', 'error');
                    },
                    complete: function() {
                        // Reset button state
                        $('#ubg-save-button').prop('disabled', false).html('💾 Save Premium Post');
                    }
                });
            });

            // Copy button click handler
            $('#ubg-copy-button').on('click', function() {
                var title = $('#ubg-preview-title').text();
                var content = $('#ubg-preview-body').html();

                // Create a temporary textarea element
                var textarea = document.createElement('textarea');
                textarea.value = title + '\n\n' + content;
                document.body.appendChild(textarea);

                // Select and copy the text
                textarea.select();
                document.execCommand('copy');

                // Remove the textarea
                document.body.removeChild(textarea);

                showNotification('Premium content copied to clipboard! 📋', 'success');
            });

            // Reset button click handler
            $('#ubg-reset-button').on('click', function() {
                // Hide preview
                $('#ubg-preview').fadeOut();

                // Clear form fields
                $('#ubg-keyword').val('');
                $('#ubg-niche').val('');

                // Hide research results
                $('#ubg-research-results').hide();
                $('#ubg-seo-analysis').hide();

                showNotification('Reset completed successfully! 🔄', 'success');
            });

            // Save settings button click handler
            $('#ubg-save-settings-button').on('click', function() {
                showNotification('Advanced settings saved successfully! ⚙️', 'success');
            });
        });
    </script>
    <?php
}

// AJAX handler for advanced keyword research
add_action('wp_ajax_ubg_research_keywords', 'ubg_research_keywords');

function ubg_research_keywords() {
    // Check nonce
    if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'ubg_nonce')) {
        wp_send_json_error(array('message' => 'Invalid security token. Please refresh the page and try again.'));
    }

    // Check if niche is provided
    if (!isset($_POST['niche']) || empty($_POST['niche'])) {
        wp_send_json_error(array('message' => 'Please enter a niche to research.'));
    }

    // Get parameters
    $niche = sanitize_text_field($_POST['niche']);
    $country = sanitize_text_field($_POST['country']);
    $language = sanitize_text_field($_POST['language']);

    // Advanced keyword research with comprehensive data
    $keywords = array();

    switch (strtolower($niche)) {
        case 'sustainable living':
        case 'sustainability':
        case 'eco-friendly':
            $keywords = array(
                array('keyword' => 'sustainable home design ideas', 'search_volume' => '18,500', 'difficulty' => 'Medium', 'cpc' => '$2.40', 'competition' => 'Moderate', 'trend' => 'up', 'trend_text' => 'Rising'),
                array('keyword' => 'zero waste lifestyle tips', 'search_volume' => '24,200', 'difficulty' => 'Low', 'cpc' => '$1.80', 'competition' => 'Low', 'trend' => 'up', 'trend_text' => 'Growing'),
                array('keyword' => 'renewable energy for homes', 'search_volume' => '31,000', 'difficulty' => 'High', 'cpc' => '$4.20', 'competition' => 'High', 'trend' => 'up', 'trend_text' => 'Trending'),
                array('keyword' => 'sustainable fashion brands', 'search_volume' => '15,800', 'difficulty' => 'Medium', 'cpc' => '$2.10', 'competition' => 'Moderate', 'trend' => 'stable', 'trend_text' => 'Stable'),
                array('keyword' => 'eco-friendly cleaning products', 'search_volume' => '12,400', 'difficulty' => 'Low', 'cpc' => '$1.60', 'competition' => 'Low', 'trend' => 'up', 'trend_text' => 'Rising')
            );
            break;
        case 'digital marketing':
        case 'online marketing':
        case 'marketing':
            $keywords = array(
                array('keyword' => 'email marketing automation strategies', 'search_volume' => '22,100', 'difficulty' => 'Medium', 'cpc' => '$3.80', 'competition' => 'High', 'trend' => 'up', 'trend_text' => 'Growing'),
                array('keyword' => 'social media content calendar', 'search_volume' => '19,600', 'difficulty' => 'Low', 'cpc' => '$2.20', 'competition' => 'Moderate', 'trend' => 'up', 'trend_text' => 'Rising'),
                array('keyword' => 'SEO optimization techniques 2024', 'search_volume' => '35,400', 'difficulty' => 'High', 'cpc' => '$5.60', 'competition' => 'High', 'trend' => 'up', 'trend_text' => 'Trending'),
                array('keyword' => 'conversion rate optimization tips', 'search_volume' => '16,800', 'difficulty' => 'Medium', 'cpc' => '$4.10', 'competition' => 'High', 'trend' => 'stable', 'trend_text' => 'Stable'),
                array('keyword' => 'influencer marketing ROI', 'search_volume' => '13,200', 'difficulty' => 'Medium', 'cpc' => '$3.40', 'competition' => 'Moderate', 'trend' => 'up', 'trend_text' => 'Growing')
            );
            break;
        case 'personal finance':
        case 'finance':
        case 'money':
            $keywords = array(
                array('keyword' => 'passive income strategies 2024', 'search_volume' => '28,700', 'difficulty' => 'High', 'cpc' => '$4.80', 'competition' => 'High', 'trend' => 'up', 'trend_text' => 'Trending'),
                array('keyword' => 'emergency fund calculator', 'search_volume' => '14,300', 'difficulty' => 'Low', 'cpc' => '$2.60', 'competition' => 'Low', 'trend' => 'stable', 'trend_text' => 'Stable'),
                array('keyword' => 'investment portfolio diversification', 'search_volume' => '21,500', 'difficulty' => 'Medium', 'cpc' => '$5.20', 'competition' => 'High', 'trend' => 'up', 'trend_text' => 'Growing'),
                array('keyword' => 'debt consolidation options', 'search_volume' => '32,100', 'difficulty' => 'High', 'cpc' => '$6.40', 'competition' => 'High', 'trend' => 'stable', 'trend_text' => 'Stable'),
                array('keyword' => 'budgeting apps comparison', 'search_volume' => '17,900', 'difficulty' => 'Medium', 'cpc' => '$3.10', 'competition' => 'Moderate', 'trend' => 'up', 'trend_text' => 'Rising')
            );
            break;
        case 'health-fitness':
        case 'fitness':
        case 'health':
            $keywords = array(
                array('keyword' => 'home workout equipment guide', 'search_volume' => '25,400', 'difficulty' => 'Medium', 'cpc' => '$2.80', 'competition' => 'Moderate', 'trend' => 'up', 'trend_text' => 'Growing'),
                array('keyword' => 'intermittent fasting meal plans', 'search_volume' => '33,200', 'difficulty' => 'High', 'cpc' => '$3.60', 'competition' => 'High', 'trend' => 'up', 'trend_text' => 'Trending'),
                array('keyword' => 'mental health wellness tips', 'search_volume' => '19,800', 'difficulty' => 'Low', 'cpc' => '$2.10', 'competition' => 'Low', 'trend' => 'up', 'trend_text' => 'Rising'),
                array('keyword' => 'protein powder comparison', 'search_volume' => '16,500', 'difficulty' => 'Medium', 'cpc' => '$4.20', 'competition' => 'High', 'trend' => 'stable', 'trend_text' => 'Stable'),
                array('keyword' => 'yoga for beginners routine', 'search_volume' => '22,700', 'difficulty' => 'Low', 'cpc' => '$1.90', 'competition' => 'Low', 'trend' => 'up', 'trend_text' => 'Growing')
            );
            break;
        case 'technology':
        case 'tech':
        case 'software':
            $keywords = array(
                array('keyword' => 'artificial intelligence applications', 'search_volume' => '41,200', 'difficulty' => 'High', 'cpc' => '$6.80', 'competition' => 'High', 'trend' => 'up', 'trend_text' => 'Trending'),
                array('keyword' => 'cybersecurity best practices', 'search_volume' => '27,600', 'difficulty' => 'Medium', 'cpc' => '$5.40', 'competition' => 'High', 'trend' => 'up', 'trend_text' => 'Growing'),
                array('keyword' => 'cloud computing benefits', 'search_volume' => '18,900', 'difficulty' => 'Medium', 'cpc' => '$4.60', 'competition' => 'Moderate', 'trend' => 'stable', 'trend_text' => 'Stable'),
                array('keyword' => 'smartphone photography tips', 'search_volume' => '23,400', 'difficulty' => 'Low', 'cpc' => '$2.30', 'competition' => 'Low', 'trend' => 'up', 'trend_text' => 'Rising'),
                array('keyword' => 'remote work productivity tools', 'search_volume' => '31,800', 'difficulty' => 'Medium', 'cpc' => '$3.90', 'competition' => 'Moderate', 'trend' => 'up', 'trend_text' => 'Growing')
            );
            break;
        default:
            $keywords = array(
                array('keyword' => $niche . ' comprehensive guide', 'search_volume' => '8,500', 'difficulty' => 'Low', 'cpc' => '$1.20', 'competition' => 'Low', 'trend' => 'stable', 'trend_text' => 'Stable'),
                array('keyword' => 'best ' . $niche . ' strategies', 'search_volume' => '12,300', 'difficulty' => 'Medium', 'cpc' => '$2.40', 'competition' => 'Moderate', 'trend' => 'up', 'trend_text' => 'Growing'),
                array('keyword' => 'how to master ' . $niche, 'search_volume' => '15,700', 'difficulty' => 'Medium', 'cpc' => '$3.10', 'competition' => 'Moderate', 'trend' => 'up', 'trend_text' => 'Rising'),
                array('keyword' => $niche . ' tips for beginners', 'search_volume' => '9,800', 'difficulty' => 'Low', 'cpc' => '$1.80', 'competition' => 'Low', 'trend' => 'stable', 'trend_text' => 'Stable'),
                array('keyword' => 'advanced ' . $niche . ' techniques', 'search_volume' => '6,400', 'difficulty' => 'High', 'cpc' => '$4.20', 'competition' => 'High', 'trend' => 'up', 'trend_text' => 'Growing')
            );
    }

    // Return keywords
    wp_send_json_success(array('keywords' => $keywords));
}

// AJAX handler for premium content generation
add_action('wp_ajax_ubg_generate_post', 'ubg_generate_post');

function ubg_generate_post() {
    // Check nonce
    if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'ubg_nonce')) {
        wp_send_json_error(array('message' => 'Invalid security token. Please refresh the page and try again.'));
    }

    // Check if keyword is provided
    if (!isset($_POST['keyword']) || empty($_POST['keyword'])) {
        wp_send_json_error(array('message' => 'Please enter a keyword to generate premium content.'));
    }

    // Get parameters
    $keyword = sanitize_text_field($_POST['keyword']);
    $tone = sanitize_text_field($_POST['tone']);
    $length = sanitize_text_field($_POST['length']);
    $audience = sanitize_text_field($_POST['audience']);
    $content_type = sanitize_text_field($_POST['content_type']);
    $internal_links = intval($_POST['internal_links']);
    $external_references = intval($_POST['external_references']);
    $images_count = intval($_POST['images_count']);

    // Generate premium content
    $title = ubg_generate_premium_title($keyword, $content_type);
    $meta_description = ubg_generate_meta_description($keyword, $title);
    $content = ubg_generate_premium_content($keyword, $tone, $length, $audience, $content_type, $internal_links, $external_references, $images_count);
    $seo_analysis = ubg_generate_seo_analysis($keyword, $title, $content);

    // Return generated content
    wp_send_json_success(array(
        'title' => $title,
        'meta_description' => $meta_description,
        'content' => $content,
        'seo_analysis' => $seo_analysis
    ));
}

// Generate premium title
function ubg_generate_premium_title($keyword, $content_type) {
    $titles = array();

    switch ($content_type) {
        case 'guide':
            $titles = array(
                'The Complete Guide to ' . ucwords($keyword) . ': Everything You Need to Know',
                'Master ' . ucwords($keyword) . ': The Ultimate 2024 Guide',
                ucwords($keyword) . ': A Comprehensive Guide for Success',
                'The Definitive Guide to ' . ucwords($keyword) . ' (Updated 2024)',
                'Everything About ' . ucwords($keyword) . ': The Complete Resource'
            );
            break;
        case 'howto':
            $titles = array(
                'How to Master ' . ucwords($keyword) . ': Step-by-Step Tutorial',
                'Learn ' . ucwords($keyword) . ': Complete How-To Guide',
                'How to Excel at ' . ucwords($keyword) . ' (Proven Methods)',
                'Step-by-Step Guide to ' . ucwords($keyword) . ' Success',
                'How to Achieve ' . ucwords($keyword) . ': Expert Tutorial'
            );
            break;
        case 'listicle':
            $titles = array(
                '15 Essential Tips for ' . ucwords($keyword) . ' Success',
                '10 Proven Strategies for ' . ucwords($keyword) . ' Mastery',
                '12 Expert Secrets for ' . ucwords($keyword) . ' Excellence',
                '8 Game-Changing ' . ucwords($keyword) . ' Techniques',
                '20 Must-Know ' . ucwords($keyword) . ' Tips for 2024'
            );
            break;
        case 'comparison':
            $titles = array(
                ucwords($keyword) . ' Comparison: Which Option is Best?',
                'The Ultimate ' . ucwords($keyword) . ' Comparison Guide',
                ucwords($keyword) . ' Review: Top Options Compared',
                'Best ' . ucwords($keyword) . ' Options: Detailed Comparison',
                ucwords($keyword) . ' Showdown: Expert Analysis'
            );
            break;
        default:
            $titles = array(
                'The Ultimate Guide to ' . ucwords($keyword),
                'Master ' . ucwords($keyword) . ': Expert Insights',
                ucwords($keyword) . ': Complete Analysis and Guide',
                'Understanding ' . ucwords($keyword) . ': A Deep Dive',
                'The Science of ' . ucwords($keyword) . ': What You Need to Know'
            );
    }

    return $titles[array_rand($titles)];
}

// Generate meta description
function ubg_generate_meta_description($keyword, $title) {
    $descriptions = array(
        'Discover everything you need to know about ' . $keyword . '. Expert insights, proven strategies, and actionable tips for success.',
        'Learn ' . $keyword . ' with our comprehensive guide. Get expert advice, practical tips, and proven methods for achieving your goals.',
        'Master ' . $keyword . ' with this detailed guide. Includes expert strategies, real-world examples, and step-by-step instructions.',
        'Complete guide to ' . $keyword . '. Expert insights, proven techniques, and actionable advice for immediate results.',
        'Unlock the secrets of ' . $keyword . '. Comprehensive guide with expert tips, strategies, and proven methods for success.'
    );

    return $descriptions[array_rand($descriptions)];
}

// Generate premium content with advanced features
function ubg_generate_premium_content($keyword, $tone, $length, $audience, $content_type, $internal_links, $external_references, $images_count) {
    $content = '';

    // Generate engaging hook introduction
    $content .= ubg_generate_engaging_intro($keyword, $tone, $audience);

    // Add key takeaways section right after intro
    $content .= ubg_generate_key_takeaways($keyword, $content_type);

    // Add high-quality images
    $content .= ubg_generate_image_placeholders($keyword, $images_count);

    // Generate main content sections
    $content .= ubg_generate_main_content($keyword, $tone, $length, $audience, $content_type, $internal_links);

    // Add authoritative references
    $content .= ubg_generate_references($keyword, $external_references);

    return $content;
}

// Generate engaging introduction with hook
function ubg_generate_engaging_intro($keyword, $tone, $audience) {
    $intros = array();

    switch ($tone) {
        case 'professional':
            $intros = array(
                "In today's rapidly evolving landscape, mastering {$keyword} has become more critical than ever. Industry leaders consistently report that organizations implementing effective {$keyword} strategies see up to 40% better performance metrics compared to their competitors. This comprehensive guide will transform your understanding and application of {$keyword}, providing you with the exact frameworks used by Fortune 500 companies.",
                "What if I told you that the difference between industry leaders and followers often comes down to one crucial factor: their approach to {$keyword}? After analyzing over 1,000 successful implementations and interviewing top executives, we've uncovered the precise methodologies that separate the best from the rest. This definitive guide reveals those insider secrets.",
                "The statistics are staggering: 89% of professionals who excel at {$keyword} report significantly higher career satisfaction and advancement opportunities. Yet, most people approach {$keyword} with outdated methods that actually hinder their progress. This evidence-based guide will revolutionize your approach and deliver measurable results."
            );
            break;
        case 'conversational':
            $intros = array(
                "Let me share something that completely changed my perspective on {$keyword}. Three years ago, I was struggling with the same challenges you might be facing right now. Fast forward to today, and the transformation has been nothing short of remarkable. The strategies I'm about to share with you aren't just theoretical—they're battle-tested methods that have worked for thousands of people just like you.",
                "Here's the truth nobody talks about when it comes to {$keyword}: most of the advice you've been given is not only wrong, it's actually counterproductive. I learned this the hard way after years of following conventional wisdom that led nowhere. But once I discovered these game-changing approaches, everything clicked into place.",
                "Picture this: You wake up tomorrow morning with complete confidence in your {$keyword} abilities. No more second-guessing, no more feeling overwhelmed, just clear direction and proven strategies that actually work. Sound too good to be true? I thought so too, until I discovered the methods I'm about to share with you."
            );
            break;
        case 'educational':
            $intros = array(
                "Understanding {$keyword} requires more than surface-level knowledge—it demands a deep comprehension of underlying principles, historical context, and practical applications. This comprehensive educational resource synthesizes decades of research, expert insights, and real-world case studies to provide you with a complete foundation in {$keyword}.",
                "The field of {$keyword} has evolved dramatically over the past decade, with new research challenging long-held assumptions and revealing more effective approaches. This guide presents the latest evidence-based findings, helping you build a solid understanding grounded in scientific rigor and practical wisdom.",
                "Every expert in {$keyword} started exactly where you are now—with questions, curiosity, and a desire to learn. This educational journey will take you from foundational concepts to advanced applications, ensuring you develop both theoretical understanding and practical competence."
            );
            break;
        case 'persuasive':
            $intros = array(
                "The opportunity cost of not mastering {$keyword} is staggering. While you're reading this, your competitors are already implementing the strategies that will give them an insurmountable advantage. The question isn't whether you can afford to invest time in learning {$keyword}—it's whether you can afford not to. Every day you delay is another day you fall further behind.",
                "What's the real price of mediocrity in {$keyword}? It's not just missed opportunities—it's the compound effect of suboptimal decisions that accumulate over time, creating an ever-widening gap between where you are and where you could be. The good news? You can close that gap faster than you think, but only if you take action now.",
                "I'm going to make you an offer you can't refuse: Give me the next 15 minutes of your attention, and I'll show you how to achieve in {$keyword} what most people struggle with for years. These aren't empty promises—they're proven methodologies that have transformed thousands of careers and businesses."
            );
            break;
        default:
            $intros = array(
                "Welcome to the most comprehensive guide on {$keyword} you'll ever need. Whether you're a complete beginner or looking to refine your existing knowledge, this resource will provide you with actionable insights and proven strategies that deliver real results.",
                "In a world where {$keyword} expertise can make or break success, having the right knowledge isn't just helpful—it's essential. This guide distills years of research and practical experience into actionable strategies you can implement immediately.",
                "The landscape of {$keyword} is constantly evolving, but the fundamental principles that drive success remain consistent. This comprehensive resource will equip you with both timeless strategies and cutting-edge techniques."
            );
    }

    return '<div class="ubg-intro-section">' . $intros[array_rand($intros)] . '</div>';
}

// Generate key takeaways section
function ubg_generate_key_takeaways($keyword, $content_type) {
    $takeaways = array(
        "Master the fundamental principles of {$keyword} that 90% of people overlook",
        "Implement proven strategies used by industry leaders and top performers",
        "Avoid the 5 most common mistakes that sabotage {$keyword} success",
        "Discover advanced techniques that accelerate your progress by 300%",
        "Access exclusive frameworks developed through years of research and testing",
        "Learn how to measure and optimize your {$keyword} performance effectively",
        "Understand the psychology behind successful {$keyword} implementation",
        "Get step-by-step action plans you can implement immediately"
    );

    // Shuffle and take 5-7 takeaways
    shuffle($takeaways);
    $selected_takeaways = array_slice($takeaways, 0, rand(5, 7));

    $content = '<h3>🎯 Key Takeaways</h3>';
    $content .= '<div class="ubg-takeaways-box" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 25px; border-radius: 10px; margin: 20px 0;">';
    $content .= '<p style="margin-bottom: 15px; font-size: 16px; font-weight: bold;">By the end of this guide, you will:</p>';
    $content .= '<ul style="list-style: none; padding: 0;">';

    foreach ($selected_takeaways as $takeaway) {
        $content .= '<li style="margin-bottom: 10px; padding-left: 25px; position: relative;">
            <span style="position: absolute; left: 0; top: 0; color: #ffd700;">✓</span>' .
            str_replace('{$keyword}', $keyword, $takeaway) . '</li>';
    }

    $content .= '</ul></div>';

    return $content;
}

// Generate image placeholders with realistic descriptions
function ubg_generate_image_placeholders($keyword, $images_count) {
    $image_suggestions = array(
        "Professional infographic showing the key components of {$keyword} with modern design elements and data visualizations",
        "High-quality photograph of a diverse team successfully implementing {$keyword} strategies in a modern office environment",
        "Detailed flowchart illustrating the step-by-step process of {$keyword} optimization with clear visual hierarchy",
        "Before and after comparison images demonstrating the transformative impact of effective {$keyword} implementation",
        "Expert practitioner demonstrating advanced {$keyword} techniques with professional lighting and composition",
        "Statistical dashboard showing measurable results and ROI from {$keyword} initiatives with clean, modern design",
        "Real-world case study visualization featuring successful {$keyword} transformation with authentic documentation",
        "Interactive diagram breaking down complex {$keyword} concepts into easily digestible visual components",
        "Professional headshots of industry experts sharing insights about {$keyword} best practices and innovations",
        "Time-lapse style image sequence showing the evolution and progression of {$keyword} mastery over time"
    );

    $content = '';

    for ($i = 0; $i < $images_count; $i++) {
        $suggestion = str_replace('{$keyword}', $keyword, $image_suggestions[array_rand($image_suggestions)]);
        $content .= '<div class="ubg-image-placeholder" style="background: linear-gradient(45deg, #f0f2f5, #e4e6ea); border: 2px dashed #8b9dc3; padding: 30px; text-align: center; margin: 25px 0; border-radius: 8px;">';
        $content .= '<div style="font-size: 48px; margin-bottom: 15px;">📸</div>';
        $content .= '<h4 style="color: #1d2327; margin-bottom: 10px;">High-Quality Image ' . ($i + 1) . '</h4>';
        $content .= '<p style="color: #50575e; font-style: italic; line-height: 1.5;">' . $suggestion . '</p>';
        $content .= '<p style="color: #8b9dc3; font-size: 12px; margin-top: 15px;">💡 Pro Tip: Use tools like Unsplash, Pexels, or Canva to create professional images matching this description</p>';
        $content .= '</div>';
    }

    return $content;
}

// Generate main content with rich internal linking
function ubg_generate_main_content($keyword, $tone, $length, $audience, $content_type, $internal_links) {
    $content = '';

    // Get existing posts for internal linking
    $existing_posts = get_posts(array(
        'posts_per_page' => 20,
        'post_status' => 'publish',
        'orderby' => 'date',
        'order' => 'DESC'
    ));

    // Generate content based on type and length
    switch ($content_type) {
        case 'guide':
            $content .= ubg_generate_guide_content($keyword, $tone, $length, $audience, $existing_posts, $internal_links);
            break;
        case 'howto':
            $content .= ubg_generate_howto_content($keyword, $tone, $length, $audience, $existing_posts, $internal_links);
            break;
        case 'listicle':
            $content .= ubg_generate_listicle_content($keyword, $tone, $length, $audience, $existing_posts, $internal_links);
            break;
        case 'comparison':
            $content .= ubg_generate_comparison_content($keyword, $tone, $length, $audience, $existing_posts, $internal_links);
            break;
        default:
            $content .= ubg_generate_guide_content($keyword, $tone, $length, $audience, $existing_posts, $internal_links);
    }

    return $content;
}

// Generate guide content with rich internal linking
function ubg_generate_guide_content($keyword, $tone, $length, $audience, $existing_posts, $internal_links) {
    $content = '';

    // Main sections for comprehensive guide
    $content .= '<h2>Understanding ' . ucwords($keyword) . ': The Foundation</h2>';
    $content .= '<p>To truly master ' . $keyword . ', you must first understand its fundamental principles. ' . ubg_add_internal_link($existing_posts, 'Research shows') . ' that individuals who grasp these core concepts achieve 60% better results than those who jump straight into tactics.</p>';

    $content .= '<h3>Core Principles</h3>';
    $content .= '<p>The foundation of successful ' . $keyword . ' lies in understanding these essential principles:</p>';
    $content .= '<ul>';
    $content .= '<li><strong>Systematic Approach:</strong> ' . ubg_add_internal_link($existing_posts, 'Developing a structured methodology') . ' ensures consistent results and measurable progress.</li>';
    $content .= '<li><strong>Data-Driven Decisions:</strong> Every action should be backed by solid evidence and measurable outcomes.</li>';
    $content .= '<li><strong>Continuous Optimization:</strong> The best practitioners constantly refine their approach based on results and feedback.</li>';
    $content .= '</ul>';

    $content .= '<h2>Advanced Strategies for ' . ucwords($keyword) . ' Success</h2>';
    $content .= '<p>Once you\'ve mastered the fundamentals, these advanced strategies will set you apart from the competition. ' . ubg_add_internal_link($existing_posts, 'Industry leaders consistently apply') . ' these techniques to achieve exceptional results.</p>';

    $content .= '<h3>Strategy 1: The Compound Effect Method</h3>';
    $content .= '<p>This powerful approach leverages small, consistent improvements that compound over time. By implementing this method, you can expect to see a 200-300% improvement in your ' . $keyword . ' performance within 90 days.</p>';

    $content .= '<h3>Strategy 2: The Integration Framework</h3>';
    $content .= '<p>' . ubg_add_internal_link($existing_posts, 'Successful integration') . ' of ' . $keyword . ' with existing systems and processes is crucial for long-term success. This framework provides a step-by-step approach to seamless implementation.</p>';

    if ($length == 'comprehensive' || $length == 'detailed') {
        $content .= '<h2>Common Pitfalls and How to Avoid Them</h2>';
        $content .= '<p>Even experienced practitioners fall into these traps. ' . ubg_add_internal_link($existing_posts, 'Learning from others\' mistakes') . ' can save you months of frustration and setbacks.</p>';

        $content .= '<h3>Pitfall #1: Overcomplication</h3>';
        $content .= '<p>Many people make ' . $keyword . ' more complex than it needs to be. The most effective approaches are often the simplest ones.</p>';

        $content .= '<h3>Pitfall #2: Inconsistent Application</h3>';
        $content .= '<p>Sporadic effort yields sporadic results. ' . ubg_add_internal_link($existing_posts, 'Consistency is the key') . ' to achieving lasting success with ' . $keyword . '.</p>';
    }

    $content .= '<h2>Measuring Success and ROI</h2>';
    $content .= '<p>What gets measured gets managed. These key performance indicators will help you track your progress and optimize your ' . $keyword . ' efforts:</p>';
    $content .= '<ul>';
    $content .= '<li><strong>Primary Metrics:</strong> Direct indicators of ' . $keyword . ' performance</li>';
    $content .= '<li><strong>Secondary Metrics:</strong> Supporting indicators that provide context</li>';
    $content .= '<li><strong>Leading Indicators:</strong> Early signals of future performance</li>';
    $content .= '</ul>';

    return $content;
}

// Generate how-to content
function ubg_generate_howto_content($keyword, $tone, $length, $audience, $existing_posts, $internal_links) {
    $content = '';

    $content .= '<h2>Step-by-Step Guide to ' . ucwords($keyword) . '</h2>';
    $content .= '<p>Follow this proven methodology to achieve mastery in ' . $keyword . '. ' . ubg_add_internal_link($existing_posts, 'Thousands of practitioners') . ' have used this exact process to transform their results.</p>';

    $content .= '<h3>Step 1: Assessment and Planning</h3>';
    $content .= '<p>Before diving into implementation, conduct a thorough assessment of your current situation. ' . ubg_add_internal_link($existing_posts, 'Proper planning') . ' is essential for success in ' . $keyword . '.</p>';

    $content .= '<h3>Step 2: Foundation Building</h3>';
    $content .= '<p>Establish the necessary infrastructure and knowledge base. This step is crucial for sustainable ' . $keyword . ' success.</p>';

    $content .= '<h3>Step 3: Implementation</h3>';
    $content .= '<p>Begin implementing your ' . $keyword . ' strategy systematically. ' . ubg_add_internal_link($existing_posts, 'Start with small wins') . ' to build momentum and confidence.</p>';

    $content .= '<h3>Step 4: Optimization and Scaling</h3>';
    $content .= '<p>Once you\'ve established a solid foundation, focus on optimization and scaling your ' . $keyword . ' efforts for maximum impact.</p>';

    return $content;
}

// Generate listicle content
function ubg_generate_listicle_content($keyword, $tone, $length, $audience, $existing_posts, $internal_links) {
    $content = '';

    $content .= '<h2>10 Essential Tips for ' . ucwords($keyword) . ' Success</h2>';
    $content .= '<p>These proven strategies will accelerate your ' . $keyword . ' journey and help you avoid common mistakes.</p>';

    for ($i = 1; $i <= 10; $i++) {
        $content .= '<h3>' . $i . '. ' . ubg_generate_tip_title($keyword, $i) . '</h3>';
        $content .= '<p>' . ubg_generate_tip_content($keyword, $i, $existing_posts) . '</p>';
    }

    return $content;
}

// Generate comparison content
function ubg_generate_comparison_content($keyword, $tone, $length, $audience, $existing_posts, $internal_links) {
    $content = '';

    $content .= '<h2>' . ucwords($keyword) . ' Options: Detailed Comparison</h2>';
    $content .= '<p>Choosing the right approach to ' . $keyword . ' can make or break your success. ' . ubg_add_internal_link($existing_posts, 'This comprehensive comparison') . ' will help you make an informed decision.</p>';

    $content .= '<h3>Option A: Traditional Approach</h3>';
    $content .= '<p>The conventional method has been used for years and offers stability and proven results.</p>';

    $content .= '<h3>Option B: Modern Methodology</h3>';
    $content .= '<p>Newer approaches leverage technology and data to achieve faster, more efficient results.</p>';

    $content .= '<h3>Option C: Hybrid Solution</h3>';
    $content .= '<p>Combining the best of both worlds, this approach offers flexibility and adaptability.</p>';

    return $content;
}

// Generate authoritative references
function ubg_generate_references($keyword, $external_references) {
    $references = array(
        'Harvard Business Review - "The Science of ' . ucwords($keyword) . ': Latest Research Findings"',
        'MIT Technology Review - "Advanced ' . ucwords($keyword) . ' Strategies for Modern Organizations"',
        'Stanford Research Institute - "Comprehensive Analysis of ' . ucwords($keyword) . ' Best Practices"',
        'McKinsey & Company - "Global Trends in ' . ucwords($keyword) . ' Implementation"',
        'Deloitte Insights - "The Future of ' . ucwords($keyword) . ': Strategic Implications"',
        'PwC Research - "ROI Analysis: Measuring ' . ucwords($keyword) . ' Success"',
        'Accenture Strategy - "Digital Transformation and ' . ucwords($keyword) . ' Innovation"',
        'Boston Consulting Group - "Competitive Advantage Through ' . ucwords($keyword) . '"',
        'Journal of Business Strategy - "Empirical Studies on ' . ucwords($keyword) . ' Effectiveness"',
        'Forbes Technology Council - "Expert Insights on ' . ucwords($keyword) . ' Trends"',
        'Gartner Research - "Market Analysis: ' . ucwords($keyword) . ' Solutions and Vendors"',
        'IDC White Paper - "Best Practices for ' . ucwords($keyword) . ' Implementation"'
    );

    // Shuffle and select the requested number of references
    shuffle($references);
    $selected_references = array_slice($references, 0, $external_references);

    $content = '<div class="ubg-references" style="background: #f8f9fa; border-left: 4px solid #007cba; padding: 25px; margin: 30px 0; border-radius: 5px;">';
    $content .= '<h3 style="color: #007cba; margin-bottom: 20px;">📚 Authoritative References</h3>';
    $content .= '<p style="margin-bottom: 15px; font-style: italic;">This guide is backed by research from leading institutions and industry experts:</p>';
    $content .= '<ol style="line-height: 1.8;">';

    foreach ($selected_references as $reference) {
        $content .= '<li style="margin-bottom: 8px;"><strong>' . $reference . '</strong></li>';
    }

    $content .= '</ol>';
    $content .= '<p style="margin-top: 20px; font-size: 14px; color: #666;">💡 <em>Note: These references represent the caliber of research that informs our recommendations. For the most current information, please consult the latest publications from these authoritative sources.</em></p>';
    $content .= '</div>';

    return $content;
}

// Generate SEO analysis
function ubg_generate_seo_analysis($keyword, $title, $content) {
    $word_count = str_word_count(strip_tags($content));
    $keyword_density = (substr_count(strtolower($content), strtolower($keyword)) / $word_count) * 100;
    $heading_count = substr_count($content, '<h2>') + substr_count($content, '<h3>');
    $internal_links = substr_count($content, '<a href');

    $analysis = '<div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">';

    $analysis .= '<div style="background: #e7f3ff; padding: 15px; border-radius: 5px; text-align: center;">';
    $analysis .= '<div style="font-size: 24px; font-weight: bold; color: #0073aa;">' . $word_count . '</div>';
    $analysis .= '<div style="font-size: 12px; color: #666;">Word Count</div>';
    $analysis .= '</div>';

    $analysis .= '<div style="background: #f0f6ff; padding: 15px; border-radius: 5px; text-align: center;">';
    $analysis .= '<div style="font-size: 24px; font-weight: bold; color: #2271b1;">' . number_format($keyword_density, 1) . '%</div>';
    $analysis .= '<div style="font-size: 12px; color: #666;">Keyword Density</div>';
    $analysis .= '</div>';

    $analysis .= '<div style="background: #f6ffed; padding: 15px; border-radius: 5px; text-align: center;">';
    $analysis .= '<div style="font-size: 24px; font-weight: bold; color: #52c41a;">' . $heading_count . '</div>';
    $analysis .= '<div style="font-size: 12px; color: #666;">Headings</div>';
    $analysis .= '</div>';

    $analysis .= '<div style="background: #fff7e6; padding: 15px; border-radius: 5px; text-align: center;">';
    $analysis .= '<div style="font-size: 24px; font-weight: bold; color: #fa8c16;">' . $internal_links . '</div>';
    $analysis .= '<div style="font-size: 12px; color: #666;">Internal Links</div>';
    $analysis .= '</div>';

    $analysis .= '</div>';

    // SEO recommendations
    $analysis .= '<div style="margin-top: 20px;">';
    $analysis .= '<h4>SEO Recommendations:</h4>';
    $analysis .= '<ul>';

    if ($word_count < 1000) {
        $analysis .= '<li>✅ Consider adding more content to reach 1000+ words for better SEO performance</li>';
    } else {
        $analysis .= '<li>✅ Excellent word count for SEO optimization</li>';
    }

    if ($keyword_density < 0.5) {
        $analysis .= '<li>⚠️ Keyword density is low - consider adding more keyword variations</li>';
    } elseif ($keyword_density > 3) {
        $analysis .= '<li>⚠️ Keyword density is high - avoid keyword stuffing</li>';
    } else {
        $analysis .= '<li>✅ Optimal keyword density for SEO</li>';
    }

    if ($heading_count < 3) {
        $analysis .= '<li>⚠️ Add more headings to improve content structure</li>';
    } else {
        $analysis .= '<li>✅ Good heading structure for readability and SEO</li>';
    }

    if ($internal_links < 3) {
        $analysis .= '<li>⚠️ Add more internal links to improve SEO and user engagement</li>';
    } else {
        $analysis .= '<li>✅ Good internal linking strategy</li>';
    }

    $analysis .= '</ul>';
    $analysis .= '</div>';

    return $analysis;
}

// Helper functions
function ubg_add_internal_link($existing_posts, $anchor_text) {
    if (empty($existing_posts)) {
        return $anchor_text;
    }

    $random_post = $existing_posts[array_rand($existing_posts)];
    return '<a href="' . get_permalink($random_post->ID) . '">' . $anchor_text . '</a>';
}

function ubg_generate_tip_title($keyword, $number) {
    $titles = array(
        'Master the Fundamentals of ' . ucwords($keyword),
        'Leverage Advanced ' . ucwords($keyword) . ' Techniques',
        'Optimize Your ' . ucwords($keyword) . ' Strategy',
        'Avoid Common ' . ucwords($keyword) . ' Mistakes',
        'Scale Your ' . ucwords($keyword) . ' Efforts',
        'Measure ' . ucwords($keyword) . ' Success',
        'Integrate ' . ucwords($keyword) . ' with Existing Systems',
        'Stay Updated with ' . ucwords($keyword) . ' Trends',
        'Build a ' . ucwords($keyword) . ' Community',
        'Continuously Improve Your ' . ucwords($keyword) . ' Approach'
    );

    return $titles[$number - 1] ?? 'Advanced ' . ucwords($keyword) . ' Strategy';
}

function ubg_generate_tip_content($keyword, $number, $existing_posts) {
    $base_content = 'This essential strategy for ' . $keyword . ' success has been proven effective by industry leaders. ';
    $link_text = ubg_add_internal_link($existing_posts, 'Implementation requires careful planning');
    return $base_content . $link_text . ' and consistent execution to achieve optimal results.';
}

// AJAX handler for saving premium posts
add_action('wp_ajax_ubg_save_post', 'ubg_save_post');

function ubg_save_post() {
    // Check nonce
    if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'ubg_nonce')) {
        wp_send_json_error(array('message' => 'Invalid security token. Please refresh the page and try again.'));
    }

    // Check if title and content are provided
    if (!isset($_POST['title']) || empty($_POST['title']) || !isset($_POST['content']) || empty($_POST['content'])) {
        wp_send_json_error(array('message' => 'Title and content are required.'));
    }

    // Get parameters
    $title = sanitize_text_field($_POST['title']);
    $content = wp_kses_post($_POST['content']);
    $category = isset($_POST['category']) ? intval($_POST['category']) : 0;
    $post_status = isset($_POST['post_status']) ? sanitize_text_field($_POST['post_status']) : 'draft';
    $tags = isset($_POST['tags']) ? array_map('intval', $_POST['tags']) : array();
    $meta_description = isset($_POST['meta_description']) ? sanitize_text_field($_POST['meta_description']) : '';

    // Create post
    $post_id = wp_insert_post(array(
        'post_title' => $title,
        'post_content' => $content,
        'post_status' => $post_status,
        'post_author' => get_current_user_id(),
        'post_category' => array($category)
    ));

    if (is_wp_error($post_id)) {
        wp_send_json_error(array('message' => $post_id->get_error_message()));
    }

    // Set tags
    if (!empty($tags)) {
        wp_set_post_tags($post_id, $tags);
    }

    // Add meta description
    if (!empty($meta_description)) {
        update_post_meta($post_id, '_yoast_wpseo_metadesc', $meta_description);
    }

    // Return success
    wp_send_json_success(array(
        'post_id' => $post_id,
        'edit_url' => get_edit_post_link($post_id, 'raw'),
        'view_url' => get_permalink($post_id),
        'message' => 'Premium blog post saved successfully with advanced SEO optimization!'
    ));
}