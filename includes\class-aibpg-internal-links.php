<?php
/**
 * Advanced Internal Linking System for AI Blog Post Generator
 *
 * This class provides sophisticated internal linking capabilities to boost SEO
 * by analyzing the sitemap, content relevance, and link equity distribution.
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

class AIBPG_Internal_Links {
    /**
     * Constructor
     */
    public function __construct() {
        // Nothing to do here for now
    }

    /**
     * Get optimal internal links for a given content and keyword
     *
     * @param string $content The content to analyze
     * @param string $keyword The main keyword
     * @param int $count Number of links to include (default: 6)
     * @return array Internal links (URL => Title)
     */
    public function get_optimal_internal_links($content, $keyword, $count = 6) {
        // Get sitemap data for analysis
        $sitemap_data = $this->analyze_sitemap();

        // Get most relevant posts based on content and keyword
        $relevant_posts = $this->get_relevant_posts($content, $keyword, $count * 2);

        // Get most strategic posts (high authority pages)
        $strategic_posts = $this->get_strategic_posts($count * 2);

        // Combine and prioritize links
        $combined_links = $this->prioritize_links($relevant_posts, $strategic_posts, $count);

        return $combined_links;
    }

    /**
     * Analyze sitemap to find most important pages
     *
     * @return array Sitemap analysis data
     */
    private function analyze_sitemap() {
        // Check if we have cached sitemap data
        $sitemap_data = get_transient('aibpg_sitemap_analysis');

        if (false !== $sitemap_data) {
            return $sitemap_data;
        }

        $sitemap_data = array(
            'pages' => array(),
            'categories' => array(),
            'top_pages' => array()
        );

        // Get all published posts
        $posts = get_posts(array(
            'post_type' => array('post', 'page'),
            'post_status' => 'publish',
            'numberposts' => -1
        ));

        foreach ($posts as $post) {
            // Get post URL
            $url = get_permalink($post->ID);

            // Get post categories
            $categories = get_the_category($post->ID);
            $category_names = array();

            if ($categories) {
                foreach ($categories as $category) {
                    $category_names[] = $category->name;

                    if (!isset($sitemap_data['categories'][$category->term_id])) {
                        $sitemap_data['categories'][$category->term_id] = array(
                            'name' => $category->name,
                            'count' => 0,
                            'posts' => array()
                        );
                    }

                    $sitemap_data['categories'][$category->term_id]['count']++;
                    $sitemap_data['categories'][$category->term_id]['posts'][] = $post->ID;
                }
            }

            // Get comment count as a simple popularity metric
            $comment_count = get_comment_count($post->ID);

            // Store page data
            $sitemap_data['pages'][$post->ID] = array(
                'url' => $url,
                'title' => $post->post_title,
                'date' => $post->post_date,
                'modified' => $post->post_modified,
                'type' => $post->post_type,
                'categories' => $category_names,
                'comment_count' => $comment_count['approved'],
                'content' => $post->post_content
            );
        }

        // Sort pages by comment count to find most popular
        $pages = $sitemap_data['pages'];
        usort($pages, function($a, $b) {
            return $b['comment_count'] - $a['comment_count'];
        });

        // Get top 20 pages
        $sitemap_data['top_pages'] = array_slice($pages, 0, 20, true);

        // Cache for 1 day
        set_transient('aibpg_sitemap_analysis', $sitemap_data, DAY_IN_SECONDS);

        return $sitemap_data;
    }

    /**
     * Get most relevant posts based on content and keyword
     *
     * @param string $content The content to analyze
     * @param string $keyword The main keyword
     * @param int $count Number of posts to return
     * @return array Relevant posts (URL => Title)
     */
    private function get_relevant_posts($content, $keyword, $count) {
        $relevant_posts = array();

        // Extract important terms from content
        $important_terms = $this->extract_important_terms($content, $keyword);

        // Get posts that match these terms
        $query_args = array(
            'post_type' => 'post',
            'post_status' => 'publish',
            'posts_per_page' => $count,
            's' => implode(' OR ', $important_terms),
            'orderby' => 'relevance'
        );

        $query = new WP_Query($query_args);

        if ($query->have_posts()) {
            while ($query->have_posts()) {
                $query->the_post();
                $relevant_posts[get_permalink()] = get_the_title();
            }
            wp_reset_postdata();
        }

        return $relevant_posts;
    }

    /**
     * Extract important terms from content for relevance matching
     *
     * @param string $content The content to analyze
     * @param string $keyword The main keyword
     * @return array Important terms
     */
    private function extract_important_terms($content, $keyword) {
        // Start with the main keyword
        $terms = array($keyword);

        // Extract headings as they contain important terms
        preg_match_all('/<h[1-6]>(.*?)<\/h[1-6]>/i', $content, $headings);

        if (!empty($headings[1])) {
            foreach ($headings[1] as $heading) {
                $heading_words = explode(' ', $heading);
                foreach ($heading_words as $word) {
                    if (strlen($word) > 4) {
                        $terms[] = trim($word);
                    }
                }
            }
        }

        // Extract from keyword phrases
        $keyword_parts = explode(' ', $keyword);
        foreach ($keyword_parts as $part) {
            if (strlen($part) > 4) {
                $terms[] = trim($part);
            }
        }

        // Remove duplicates and limit to 10 terms
        $terms = array_unique($terms);
        $terms = array_slice($terms, 0, 10);

        return $terms;
    }

    /**
     * Get strategic posts (high authority pages)
     *
     * @param int $count Number of posts to return
     * @return array Strategic posts (URL => Title)
     */
    private function get_strategic_posts($count) {
        $strategic_posts = array();

        // Get posts with most inbound links (using comment count as proxy)
        $query_args = array(
            'post_type' => 'post',
            'post_status' => 'publish',
            'posts_per_page' => $count,
            'orderby' => 'comment_count',
            'order' => 'DESC'
        );

        $query = new WP_Query($query_args);

        if ($query->have_posts()) {
            while ($query->have_posts()) {
                $query->the_post();
                $strategic_posts[get_permalink()] = get_the_title();
            }
            wp_reset_postdata();
        }

        return $strategic_posts;
    }

    /**
     * Prioritize and combine links for optimal internal linking
     *
     * @param array $relevant_posts Relevant posts by content
     * @param array $strategic_posts Strategic high-authority posts
     * @param int $count Number of links to return
     * @return array Prioritized links (URL => Title)
     */
    private function prioritize_links($relevant_posts, $strategic_posts, $count) {
        $prioritized_links = array();

        // First, add links that appear in both arrays (both relevant and strategic)
        foreach ($relevant_posts as $url => $title) {
            if (array_key_exists($url, $strategic_posts)) {
                $prioritized_links[$url] = $title;
            }
        }

        // Then add remaining relevant posts
        foreach ($relevant_posts as $url => $title) {
            if (!array_key_exists($url, $prioritized_links)) {
                $prioritized_links[$url] = $title;
            }

            if (count($prioritized_links) >= $count) {
                break;
            }
        }

        // If we still need more, add strategic posts
        if (count($prioritized_links) < $count) {
            foreach ($strategic_posts as $url => $title) {
                if (!array_key_exists($url, $prioritized_links)) {
                    $prioritized_links[$url] = $title;
                }

                if (count($prioritized_links) >= $count) {
                    break;
                }
            }
        }

        // Limit to requested count
        return array_slice($prioritized_links, 0, $count, true);
    }
}
