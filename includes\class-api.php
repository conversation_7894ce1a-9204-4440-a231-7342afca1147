<?php
/**
 * API class
 *
 * @package Blog_Post_Generator_Pro
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

/**
 * API class
 */
class BPG_API {
    /**
     * Constructor
     */
    public function __construct() {
        $this->init_hooks();
    }

    /**
     * Initialize hooks
     */
    private function init_hooks() {
        // AJAX handlers
        add_action('wp_ajax_bpg_research_keywords', array($this, 'research_keywords'));
        add_action('wp_ajax_bpg_generate_post', array($this, 'generate_post'));
        add_action('wp_ajax_bpg_save_post', array($this, 'save_post'));
        add_action('wp_ajax_bpg_save_settings', array($this, 'save_settings'));
    }

    /**
     * Research keywords
     */
    public function research_keywords() {
        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'bpg_nonce')) {
            wp_send_json_error(array('message' => __('Invalid security token. Please refresh the page and try again.', 'blog-post-generator-pro')));
        }
        
        // Check if niche is provided
        if (!isset($_POST['niche']) || empty($_POST['niche'])) {
            wp_send_json_error(array('message' => __('Please enter a niche to research.', 'blog-post-generator-pro')));
        }
        
        // Get niche
        $niche = sanitize_text_field($_POST['niche']);
        
        // Simulate keyword research with sample data
        $keywords = array();
        
        switch (strtolower($niche)) {
            case 'health-fitness':
                $keywords = array(
                    array('keyword' => 'best home workouts', 'search_volume' => '12,000', 'difficulty' => 'Medium', 'cpc' => '$1.20', 'competition' => 'Moderate'),
                    array('keyword' => 'weight loss tips', 'search_volume' => '22,000', 'difficulty' => 'High', 'cpc' => '$2.50', 'competition' => 'High'),
                    array('keyword' => 'healthy breakfast ideas', 'search_volume' => '8,500', 'difficulty' => 'Low', 'cpc' => '$0.80', 'competition' => 'Low'),
                    array('keyword' => 'intermittent fasting benefits', 'search_volume' => '9,200', 'difficulty' => 'Medium', 'cpc' => '$1.50', 'competition' => 'Moderate')
                );
                break;
            case 'nutrition':
                $keywords = array(
                    array('keyword' => 'protein-rich foods', 'search_volume' => '14,500', 'difficulty' => 'Medium', 'cpc' => '$1.30', 'competition' => 'Moderate'),
                    array('keyword' => 'keto diet plan', 'search_volume' => '18,000', 'difficulty' => 'High', 'cpc' => '$2.20', 'competition' => 'High'),
                    array('keyword' => 'vegan protein sources', 'search_volume' => '7,800', 'difficulty' => 'Low', 'cpc' => '$0.90', 'competition' => 'Low'),
                    array('keyword' => 'best vitamins for energy', 'search_volume' => '6,300', 'difficulty' => 'Medium', 'cpc' => '$1.40', 'competition' => 'Moderate')
                );
                break;
            case 'technology':
                $keywords = array(
                    array('keyword' => 'best smartphones 2023', 'search_volume' => '15,500', 'difficulty' => 'High', 'cpc' => '$2.80', 'competition' => 'High'),
                    array('keyword' => 'how to speed up your computer', 'search_volume' => '9,800', 'difficulty' => 'Medium', 'cpc' => '$1.20', 'competition' => 'Moderate'),
                    array('keyword' => 'best budget laptops', 'search_volume' => '11,200', 'difficulty' => 'Medium', 'cpc' => '$1.70', 'competition' => 'Moderate'),
                    array('keyword' => 'smart home devices comparison', 'search_volume' => '7,300', 'difficulty' => 'Low', 'cpc' => '$0.95', 'competition' => 'Low')
                );
                break;
            default:
                $keywords = array(
                    array('keyword' => $niche . ' tips and tricks', 'search_volume' => '5,000', 'difficulty' => 'Low', 'cpc' => '$0.75', 'competition' => 'Low'),
                    array('keyword' => 'best ' . $niche . ' strategies', 'search_volume' => '7,500', 'difficulty' => 'Medium', 'cpc' => '$1.10', 'competition' => 'Moderate'),
                    array('keyword' => 'how to improve ' . $niche, 'search_volume' => '10,000', 'difficulty' => 'High', 'cpc' => '$1.80', 'competition' => 'High')
                );
        }
        
        // Return keywords
        wp_send_json_success(array('keywords' => $keywords));
    }

    /**
     * Generate post
     */
    public function generate_post() {
        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'bpg_nonce')) {
            wp_send_json_error(array('message' => __('Invalid security token. Please refresh the page and try again.', 'blog-post-generator-pro')));
        }
        
        // Check if keyword is provided
        if (!isset($_POST['keyword']) || empty($_POST['keyword'])) {
            wp_send_json_error(array('message' => __('Please enter a keyword to generate content.', 'blog-post-generator-pro')));
        }
        
        // Get parameters
        $keyword = sanitize_text_field($_POST['keyword']);
        $tone = isset($_POST['tone']) ? sanitize_text_field($_POST['tone']) : 'professional';
        $length = isset($_POST['length']) ? sanitize_text_field($_POST['length']) : 'medium';
        
        // Generate content
        $generator = new BPG_Generator();
        $content = $generator->generate_content($keyword, $tone, $length);
        
        // Generate title
        $title = $generator->generate_title($keyword);
        
        // Return generated content
        wp_send_json_success(array(
            'title' => $title,
            'content' => $content,
            'featured_image_suggestions' => $generator->generate_image_suggestions($keyword)
        ));
    }

    /**
     * Save post
     */
    public function save_post() {
        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'bpg_nonce')) {
            wp_send_json_error(array('message' => __('Invalid security token. Please refresh the page and try again.', 'blog-post-generator-pro')));
        }
        
        // Check if title and content are provided
        if (!isset($_POST['title']) || empty($_POST['title']) || !isset($_POST['content']) || empty($_POST['content'])) {
            wp_send_json_error(array('message' => __('Title and content are required.', 'blog-post-generator-pro')));
        }
        
        // Get parameters
        $title = sanitize_text_field($_POST['title']);
        $content = wp_kses_post($_POST['content']);
        $category = isset($_POST['category']) ? intval($_POST['category']) : 0;
        $post_status = isset($_POST['post_status']) ? sanitize_text_field($_POST['post_status']) : 'draft';
        $tags = isset($_POST['tags']) ? array_map('intval', $_POST['tags']) : array();
        
        // Create post
        $post_id = wp_insert_post(array(
            'post_title' => $title,
            'post_content' => $content,
            'post_status' => $post_status,
            'post_author' => get_current_user_id(),
            'post_category' => array($category)
        ));
        
        if (is_wp_error($post_id)) {
            wp_send_json_error(array('message' => $post_id->get_error_message()));
        }
        
        // Set tags
        if (!empty($tags)) {
            wp_set_post_tags($post_id, $tags);
        }
        
        // Return success
        wp_send_json_success(array(
            'post_id' => $post_id,
            'edit_url' => get_edit_post_link($post_id, 'raw'),
            'view_url' => get_permalink($post_id),
            'message' => __('Blog post saved successfully.', 'blog-post-generator-pro')
        ));
    }

    /**
     * Save settings
     */
    public function save_settings() {
        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'bpg_nonce')) {
            wp_send_json_error(array('message' => __('Invalid security token. Please refresh the page and try again.', 'blog-post-generator-pro')));
        }
        
        // Get parameters
        $default_author = isset($_POST['default_author']) ? intval($_POST['default_author']) : get_current_user_id();
        $seo_optimization = isset($_POST['seo_optimization']) ? sanitize_text_field($_POST['seo_optimization']) : 'advanced';
        $internal_links = isset($_POST['internal_links']) ? intval($_POST['internal_links']) : 6;
        $featured_image = isset($_POST['featured_image']) ? (bool) $_POST['featured_image'] : true;
        
        // Save settings
        update_option('bpg_default_author', $default_author);
        update_option('bpg_seo_optimization', $seo_optimization);
        update_option('bpg_internal_links', $internal_links);
        update_option('bpg_featured_image', $featured_image);
        
        // Return success
        wp_send_json_success(array(
            'message' => __('Settings saved successfully.', 'blog-post-generator-pro')
        ));
    }
}
