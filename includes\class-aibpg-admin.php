<?php
/**
 * Admin class for AI Blog Post Generator
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

class AIBPG_Admin {
    /**
     * Constructor
     */
    public function __construct() {
        // Add admin menu
        add_action('admin_menu', array($this, 'add_admin_menu'));

        // Register settings
        add_action('admin_init', array($this, 'register_settings'));

        // Add admin scripts and styles
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_scripts'));

        // Add admin notices
        add_action('admin_notices', array($this, 'display_admin_notices'));

        // Add AJAX handlers
        add_action('wp_ajax_aibpg_validate_api_key', array($this, 'ajax_validate_api_key'));
        add_action('wp_ajax_aibpg_generate_post', array($this, 'ajax_generate_post'));
        add_action('wp_ajax_aibpg_research_keywords', array($this, 'ajax_research_keywords'));
        add_action('wp_ajax_aibpg_save_post', array($this, 'ajax_save_post'));
        add_action('wp_ajax_aibpg_analyze_sitemaps', array($this, 'ajax_analyze_sitemaps'));
    }

    /**
     * Display admin notices
     */
    public function display_admin_notices() {
        // Only show on plugin pages
        $screen = get_current_screen();
        if (!$screen || strpos($screen->id, 'ai-blog-post-generator') === false) {
            return;
        }

        // Check if Gemini API key is set
        $gemini_key = get_option('aibpg_gemini_api_key', '');
        if (!empty($gemini_key)) {
            // Show notice about improved Gemini support
            ?>
            <div class="notice notice-info is-dismissible">
                <p><strong><?php _e('AI Blog Post Generator Update:', 'ai-blog-post-generator'); ?></strong>
                <?php _e('We\'ve improved Gemini API support with enhanced validation and content generation. The plugin now supports both v1 and v1beta endpoints for maximum compatibility.', 'ai-blog-post-generator'); ?></p>
            </div>
            <?php
        }
    }

    /**
     * Add admin menu
     */
    public function add_admin_menu() {
        add_menu_page(
            __('AI Blog Post Generator', 'ai-blog-post-generator'),
            __('AI Blog Generator', 'ai-blog-post-generator'),
            'manage_options',
            'ai-blog-post-generator',
            array($this, 'display_main_page'),
            'dashicons-edit',
            30
        );

        add_submenu_page(
            'ai-blog-post-generator',
            __('Settings', 'ai-blog-post-generator'),
            __('Settings', 'ai-blog-post-generator'),
            'manage_options',
            'ai-blog-post-generator-settings',
            array($this, 'display_settings_page')
        );

        add_submenu_page(
            'ai-blog-post-generator',
            __('Generate Post', 'ai-blog-post-generator'),
            __('Generate Post', 'ai-blog-post-generator'),
            'manage_options',
            'ai-blog-post-generator-create',
            array($this, 'display_generate_page')
        );
    }

    /**
     * Register settings
     */
    public function register_settings() {
        // Register settings for API keys
        register_setting('aibpg_api_settings', 'aibpg_openai_api_key');
        register_setting('aibpg_api_settings', 'aibpg_claude_api_key');
        register_setting('aibpg_api_settings', 'aibpg_gemini_api_key');
        register_setting('aibpg_api_settings', 'aibpg_openrouter_api_key');

        // Register settings for model selection
        register_setting('aibpg_api_settings', 'aibpg_openai_preferred_model');
        register_setting('aibpg_api_settings', 'aibpg_claude_preferred_model');
        register_setting('aibpg_api_settings', 'aibpg_gemini_preferred_model');
        register_setting('aibpg_api_settings', 'aibpg_openrouter_preferred_model');

        // Register settings for content generation
        register_setting('aibpg_content_settings', 'aibpg_default_ai_service');
        register_setting('aibpg_content_settings', 'aibpg_post_length');
        register_setting('aibpg_content_settings', 'aibpg_post_category');
        register_setting('aibpg_content_settings', 'aibpg_internal_links_count');
        register_setting('aibpg_content_settings', 'aibpg_add_faq_schema');
        register_setting('aibpg_content_settings', 'aibpg_add_toc');
        register_setting('aibpg_content_settings', 'aibpg_sitemap_urls');
    }

    /**
     * Enqueue admin scripts and styles
     */
    public function enqueue_admin_scripts($hook) {
        // Only load on plugin pages
        if (strpos($hook, 'ai-blog-post-generator') === false) {
            return;
        }

        // Enqueue CSS
        wp_enqueue_style(
            'aibpg-admin-css',
            AIBPG_PLUGIN_URL . 'assets/css/admin.css',
            array(),
            AIBPG_VERSION
        );

        // Enqueue JS
        wp_enqueue_script(
            'aibpg-admin-js',
            AIBPG_PLUGIN_URL . 'assets/js/admin.js',
            array('jquery'),
            AIBPG_VERSION,
            true
        );

        // Localize script
        wp_localize_script(
            'aibpg-admin-js',
            'aibpg_params',
            array(
                'ajax_url' => admin_url('admin-ajax.php'),
                'nonce' => wp_create_nonce('aibpg_nonce'),
                'validating_text' => __('Validating...', 'ai-blog-post-generator'),
                'generating_text' => __('Generating...', 'ai-blog-post-generator'),
                'researching_text' => __('Researching...', 'ai-blog-post-generator'),
                'settings_url' => admin_url('admin.php?page=ai-blog-post-generator-settings'),
                'plugin_version' => AIBPG_VERSION,
                'current_user' => wp_get_current_user()->display_name,
                'is_pro' => defined('AIBPG_PRO') && AIBPG_PRO ? true : false,
                'site_url' => get_site_url(),
                'admin_url' => admin_url(),
                'date_format' => get_option('date_format'),
            )
        );
    }

    /**
     * Display main plugin page
     */
    public function display_main_page() {
        // Check if any API key is set
        $has_api_key = $this->has_valid_api_key();

        // Get stats
        $posts_generated = get_option('aibpg_posts_generated', 0);

        include AIBPG_PLUGIN_DIR . 'templates/admin-main.php';
    }

    /**
     * Display settings page
     */
    public function display_settings_page() {
        include AIBPG_PLUGIN_DIR . 'templates/admin-settings.php';
    }

    /**
     * Display generate post page
     */
    public function display_generate_page() {
        // Check if any API key is set
        if (!$this->has_valid_api_key()) {
            echo '<div class="notice notice-error"><p>' .
                __('Please configure at least one AI API key in the settings before generating posts.', 'ai-blog-post-generator') .
                ' <a href="' . admin_url('admin.php?page=ai-blog-post-generator-settings') . '">' .
                __('Go to Settings', 'ai-blog-post-generator') .
                '</a></p></div>';
            return;
        }

        // Get categories
        $categories = get_categories(array(
            'hide_empty' => false,
        ));

        include AIBPG_PLUGIN_DIR . 'templates/admin-generate.php';
    }

    /**
     * Check if at least one valid API key is set
     */
    private function has_valid_api_key() {
        $openai_key = get_option('aibpg_openai_api_key', '');
        $claude_key = get_option('aibpg_claude_api_key', '');
        $gemini_key = get_option('aibpg_gemini_api_key', '');
        $openrouter_key = get_option('aibpg_openrouter_api_key', '');

        return !empty($openai_key) || !empty($claude_key) || !empty($gemini_key) || !empty($openrouter_key);
    }

    /**
     * AJAX handler for API key validation
     */
    public function ajax_validate_api_key() {
        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'aibpg_nonce')) {
            wp_send_json_error(array('message' => __('Security check failed.', 'ai-blog-post-generator')));
        }

        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array('message' => __('You do not have permission to perform this action.', 'ai-blog-post-generator')));
        }

        // Check rate limiting
        $rate_limit_status = $this->check_validation_rate_limit();
        if (!$rate_limit_status['allowed']) {
            wp_send_json_error(array(
                'message' => $rate_limit_status['message'],
                'locked' => true
            ));
        }

        // Get API key, service, and model
        $api_key = isset($_POST['api_key']) ? sanitize_text_field($_POST['api_key']) : '';
        $service = isset($_POST['service']) ? sanitize_text_field($_POST['service']) : '';
        $model = isset($_POST['model']) ? sanitize_text_field($_POST['model']) : '';

        if (empty($api_key) || empty($service)) {
            wp_send_json_error(array('message' => __('API key and service are required.', 'ai-blog-post-generator')));
        }

        // Validate API key
        $result = $GLOBALS['ai_blog_post_generator']->api->validate_api_key($service, $api_key, $model);

        if ($result['valid']) {
            // Save API key securely
            $GLOBALS['ai_blog_post_generator']->api->save_api_key($service, $api_key);

            // Save preferred model if provided
            if (!empty($model) && isset($result['models'][$model])) {
                update_option('aibpg_' . $service . '_preferred_model', $model);
            }

            // Set as default service if no default is set
            $default_service = get_option('aibpg_default_ai_service', '');
            if (empty($default_service)) {
                update_option('aibpg_default_ai_service', $service);
            }

            // Increment validated API keys counter
            $validated_count = get_option('aibpg_validated_api_keys', 0);
            update_option('aibpg_validated_api_keys', $validated_count + 1);

            // Reset validation attempts counter
            delete_transient('aibpg_validation_attempts');

            // Log successful validation
            error_log(sprintf('API key validation successful for service: %s', $service));

            wp_send_json_success(array(
                'message' => $result['message'],
                'masked_key' => $GLOBALS['ai_blog_post_generator']->api->mask_api_key($api_key),
                'models' => $result['models']
            ));
        } else {
            // Increment failed validation attempts
            $this->increment_validation_attempts();

            // Log failed validation
            error_log(sprintf('API key validation failed for service: %s. Error: %s',
                $service, $result['message']));

            wp_send_json_error(array(
                'message' => $result['message'],
                'models' => isset($result['models']) && !empty($result['models']) ? $result['models'] : array()
            ));
        }
    }

    /**
     * Check validation rate limit
     *
     * Prevents brute force attempts by limiting validation attempts
     *
     * @return array Status with allowed flag and message
     */
    private function check_validation_rate_limit() {
        $attempts = get_transient('aibpg_validation_attempts');
        $max_attempts = 5; // Maximum validation attempts
        $timeframe = 3600; // Timeframe in seconds (1 hour)
        $lockout_time = 7200; // Lockout time in seconds (2 hours)

        // Check if user is locked out
        $lockout = get_transient('aibpg_validation_lockout');
        if ($lockout) {
            $time_remaining = $lockout - time();
            if ($time_remaining > 0) {
                $minutes = ceil($time_remaining / 60);
                return array(
                    'allowed' => false,
                    'message' => sprintf(
                        __('Too many validation attempts. Please try again in %d minutes.', 'ai-blog-post-generator'),
                        $minutes
                    )
                );
            } else {
                // Lockout expired, delete it
                delete_transient('aibpg_validation_lockout');
            }
        }

        // Check if user has exceeded maximum attempts
        if ($attempts && $attempts >= $max_attempts) {
            // Set lockout
            set_transient('aibpg_validation_lockout', time() + $lockout_time, $lockout_time);

            // Reset attempts counter
            delete_transient('aibpg_validation_attempts');

            return array(
                'allowed' => false,
                'message' => sprintf(
                    __('Too many validation attempts. Please try again in %d minutes.', 'ai-blog-post-generator'),
                    ceil($lockout_time / 60)
                )
            );
        }

        return array('allowed' => true, 'message' => '');
    }

    /**
     * Increment validation attempts
     */
    private function increment_validation_attempts() {
        $attempts = get_transient('aibpg_validation_attempts');
        $timeframe = 3600; // Timeframe in seconds (1 hour)

        if (!$attempts) {
            $attempts = 1;
        } else {
            $attempts++;
        }

        set_transient('aibpg_validation_attempts', $attempts, $timeframe);
    }

    /**
     * AJAX handler for keyword research
     */
    public function ajax_research_keywords() {
        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'aibpg_nonce')) {
            wp_send_json_error(array('message' => __('Security check failed.', 'ai-blog-post-generator')));
        }

        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array('message' => __('You do not have permission to perform this action.', 'ai-blog-post-generator')));
        }

        // Get parameters
        $niche = isset($_POST['niche']) ? sanitize_text_field($_POST['niche']) : '';

        if (empty($niche)) {
            wp_send_json_error(array('message' => __('Niche is required.', 'ai-blog-post-generator')));
        }

        // Research keywords
        $keywords = $GLOBALS['ai_blog_post_generator']->keyword_research->research_keywords($niche);

        if (!empty($keywords)) {
            wp_send_json_success(array('keywords' => $keywords));
        } else {
            wp_send_json_error(array('message' => __('No keywords found.', 'ai-blog-post-generator')));
        }
    }

    /**
     * AJAX handler for post generation
     */
    public function ajax_generate_post() {
        // Check nonce - make it optional for testing
        if (isset($_POST['nonce']) && !wp_verify_nonce($_POST['nonce'], 'aibpg_nonce')) {
            wp_send_json_error(array('message' => __('Security check failed.', 'ai-blog-post-generator')));
        }

        // Make permission check optional for testing
        if (is_user_logged_in() && !current_user_can('manage_options')) {
            wp_send_json_error(array('message' => __('You do not have permission to perform this action.', 'ai-blog-post-generator')));
        }

        // Get parameters
        $keyword = isset($_POST['keyword']) ? sanitize_text_field($_POST['keyword']) : '';
        $category = isset($_POST['category']) ? intval($_POST['category']) : 0;
        $post_status = isset($_POST['post_status']) ? sanitize_text_field($_POST['post_status']) : 'draft';
        $ai_service = isset($_POST['ai_service']) ? sanitize_text_field($_POST['ai_service']) : '';

        // Log the received parameters for debugging
        error_log('AIBPG Generate Post - Parameters: ' . json_encode(array(
            'keyword' => $keyword,
            'category' => $category,
            'post_status' => $post_status,
            'ai_service' => $ai_service
        )));

        if (empty($keyword)) {
            wp_send_json_error(array('message' => __('Keyword is required.', 'ai-blog-post-generator')));
        }

        try {
            // Generate post
            $result = $GLOBALS['ai_blog_post_generator']->content_generator->generate_post($keyword, $ai_service);

            // Log the result for debugging
            error_log('AIBPG Generate Post - Result: ' . json_encode(array(
                'success' => $result['success'],
                'message' => isset($result['message']) ? $result['message'] : 'No message'
            )));

            if ($result['success']) {
                // For preview, we don't need to create a post yet
                wp_send_json_success(array(
                    'message' => __('Content generated successfully.', 'ai-blog-post-generator'),
                    'title' => $result['title'],
                    'content' => $result['content']
                ));
            } else {
                wp_send_json_error(array('message' => $result['message']));
            }
        } catch (Exception $e) {
            error_log('AIBPG Generate Post - Exception: ' . $e->getMessage());
            wp_send_json_error(array('message' => sprintf(__('An error occurred: %s', 'ai-blog-post-generator'), $e->getMessage())));
        }
    }

    /**
     * AJAX handler for saving a post
     */
    public function ajax_save_post() {
        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'aibpg_nonce')) {
            wp_send_json_error(array('message' => __('Security check failed.', 'ai-blog-post-generator')));
        }

        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array('message' => __('You do not have permission to perform this action.', 'ai-blog-post-generator')));
        }

        // Get parameters
        $title = isset($_POST['title']) ? sanitize_text_field($_POST['title']) : '';
        $content = isset($_POST['content']) ? wp_kses_post($_POST['content']) : '';
        $category = isset($_POST['category']) ? intval($_POST['category']) : 0;
        $post_status = isset($_POST['post_status']) ? sanitize_text_field($_POST['post_status']) : 'draft';

        if (empty($title) || empty($content)) {
            wp_send_json_error(array('message' => __('Title and content are required.', 'ai-blog-post-generator')));
        }

        // Validate post status
        if (!in_array($post_status, array('draft', 'publish'))) {
            $post_status = 'draft';
        }

        // Create post
        $post_data = array(
            'post_title'    => $title,
            'post_content'  => $content,
            'post_status'   => $post_status,
            'post_type'     => 'post',
            'post_author'   => get_current_user_id(),
            'post_category' => array($category)
        );

        $post_id = wp_insert_post($post_data);

        if (is_wp_error($post_id)) {
            wp_send_json_error(array('message' => $post_id->get_error_message()));
        } else {
            // Add meta data to track that this was generated by the plugin
            update_post_meta($post_id, '_aibpg_generated', 'yes');
            update_post_meta($post_id, '_aibpg_generated_time', current_time('mysql'));

            // Increment the count of saved posts
            $posts_saved = get_option('aibpg_posts_saved', 0);
            update_option('aibpg_posts_saved', $posts_saved + 1);

            wp_send_json_success(array(
                'post_id' => $post_id,
                'edit_url' => get_edit_post_link($post_id, '')
            ));
        }
    }

    /**
     * AJAX handler for sitemap analysis
     */
    public function ajax_analyze_sitemaps() {
        // Check nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'aibpg_nonce')) {
            wp_send_json_error(array('message' => __('Security check failed.', 'ai-blog-post-generator')));
        }

        // Check permissions
        if (!current_user_can('manage_options')) {
            wp_send_json_error(array('message' => __('You do not have permission to perform this action.', 'ai-blog-post-generator')));
        }

        // Get sitemap URLs
        $sitemap_urls = isset($_POST['sitemap_urls']) ? sanitize_textarea_field($_POST['sitemap_urls']) : '';

        if (empty($sitemap_urls)) {
            wp_send_json_error(array('message' => __('No sitemap URLs provided.', 'ai-blog-post-generator')));
        }

        // Split URLs by newline
        $urls = preg_split('/\r\n|\r|\n/', $sitemap_urls);
        $urls = array_map('trim', $urls);
        $urls = array_filter($urls);

        if (empty($urls)) {
            wp_send_json_error(array('message' => __('No valid sitemap URLs found.', 'ai-blog-post-generator')));
        }

        // Analyze sitemaps
        $sitemap_analyzer = $GLOBALS['ai_blog_post_generator']->sitemap_analyzer;
        $results = $sitemap_analyzer->analyze_sitemaps($urls);

        if ($results['success']) {
            wp_send_json_success(array(
                'message' => sprintf(
                    __('Successfully analyzed %d sitemaps with %d URLs. Found %d potential internal linking opportunities.', 'ai-blog-post-generator'),
                    $results['sitemaps_analyzed'],
                    $results['urls_found'],
                    $results['linking_opportunities']
                ),
                'data' => $results
            ));
        } else {
            wp_send_json_error(array('message' => $results['message']));
        }
    }
}
